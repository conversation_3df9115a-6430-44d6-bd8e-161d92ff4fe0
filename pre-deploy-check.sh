#!/bin/bash

# 部署前安全检查脚本
# 在运行deploy-1panel.sh之前运行此脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 部署前安全检查${NC}"
echo "================================================"

# 检查当前运行的容器
echo -e "${YELLOW}📋 当前运行的容器:${NC}"
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo -e "${YELLOW}🔍 检查端口占用情况:${NC}"

# 检查将要使用的端口
ports=(8001 8080 8443 5433)
port_conflicts=false

for port in "${ports[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ 端口 $port 已被占用:${NC}"
        lsof -i :$port | head -2
        port_conflicts=true
    else
        echo -e "${GREEN}✅ 端口 $port 可用${NC}"
    fi
done

echo ""
echo -e "${YELLOW}🐳 检查Docker资源使用:${NC}"
docker system df

echo ""
echo -e "${YELLOW}💾 检查磁盘空间:${NC}"
df -h | grep -E "(Filesystem|/dev/)"

echo ""
echo -e "${YELLOW}🧠 检查内存使用:${NC}"
free -h

echo ""
echo -e "${BLUE}📋 部署计划:${NC}"
echo "将要创建的容器:"
echo "  - chinaorganicperoxide-prod-db (端口5433)"
echo "  - chinaorganicperoxide-prod-backend (端口8001)"
echo "  - chinaorganicperoxide-prod-frontend (端口8080,8443)"

echo ""
if [ "$port_conflicts" = true ]; then
    echo -e "${YELLOW}⚠️ 发现端口冲突，请确认是否继续部署${NC}"
    echo -e "${YELLOW}建议: 停止冲突的服务或修改部署端口${NC}"
else
    echo -e "${GREEN}✅ 所有检查通过，可以安全部署${NC}"
fi

echo ""
echo -e "${BLUE}🚀 如果确认无误，请运行:${NC}"
echo -e "${GREEN}./deploy-1panel.sh${NC}"
