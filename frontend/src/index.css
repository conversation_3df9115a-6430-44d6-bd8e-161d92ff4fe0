@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义菜单样式 */
.custom-menu .ant-menu-item {
  color: #ffffff !important;
}

.custom-menu .ant-menu-item:hover {
  color: #40a9ff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.custom-menu .ant-menu-item-selected {
  color: #40a9ff !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.custom-menu .ant-menu-item-icon {
  color: inherit !important;
}

/* 自定义样式 */
.ant-layout-header {
  background: #001529;
  padding: 0 24px;
}

.ant-layout-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  min-height: 280px;
}

.search-form {
  background: #f5f5f5;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.chemical-card {
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.chemical-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stats-card {
  text-align: center;
  padding: 24px;
}

.stats-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1890ff;
}

.stats-label {
  color: #666;
  margin-top: 8px;
}
