import React, { useEffect, useState } from 'react';
import { Card, Typography, Spin, Alert } from 'antd';
import { chemicalAPI } from '../services/api';

const { Text, Paragraph } = Typography;

const DebugInfo: React.FC = () => {
  const [apiData, setApiData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testAPI = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Testing API call...');
        const response = await chemicalAPI.getChemicals({ page: 1, page_size: 5 });
        console.log('API Response:', response.data);
        
        setApiData(response.data);
      } catch (err: any) {
        console.error('API Error:', err);
        setError(err.message || '未知错误');
      } finally {
        setLoading(false);
      }
    };

    testAPI();
  }, []);

  if (loading) {
    return (
      <Card title="API调试信息">
        <Spin size="large" />
        <Paragraph>正在测试API连接...</Paragraph>
      </Card>
    );
  }

  if (error) {
    return (
      <Card title="API调试信息">
        <Alert
          message="API调用失败"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  return (
    <Card title="API调试信息">
      <div style={{ marginBottom: 16 }}>
        <Text strong>API状态: </Text>
        <Text type="success">正常</Text>
      </div>
      
      <div style={{ marginBottom: 16 }}>
        <Text strong>化学品总数: </Text>
        <Text>{apiData?.count || 0}</Text>
      </div>
      
      <div style={{ marginBottom: 16 }}>
        <Text strong>返回结果数: </Text>
        <Text>{apiData?.results?.length || 0}</Text>
      </div>
      
      {apiData?.results?.length > 0 && (
        <div>
          <Text strong>第一个化学品:</Text>
          <Paragraph>
            <Text>CAS号: {apiData.results[0].cas_number}</Text><br/>
            <Text>中文名: {apiData.results[0].compound_name_cn || '无'}</Text><br/>
            <Text>英文名: {apiData.results[0].compound_name_en || '无'}</Text><br/>
            <Text>分子式: {apiData.results[0].molecular_formula || '无'}</Text>
          </Paragraph>
        </div>
      )}
      
      <details style={{ marginTop: 16 }}>
        <summary>完整API响应</summary>
        <pre style={{ background: '#f5f5f5', padding: 8, fontSize: 12 }}>
          {JSON.stringify(apiData, null, 2)}
        </pre>
      </details>
    </Card>
  );
};

export default DebugInfo;
