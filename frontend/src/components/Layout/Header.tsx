import React from 'react';
import { Layout, Menu, Typography } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  SearchOutlined,
  BarChartOutlined,
  ExperimentOutlined,
  ShopOutlined
} from '@ant-design/icons';

const { Header: AntHeader } = Layout;
const { Title } = Typography;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/search',
      icon: <SearchOutlined />,
      label: '搜索',
    },
    {
      key: '/companies',
      icon: <ShopOutlined />,
      label: '企业',
    },
    {
      key: '/statistics',
      icon: <BarChartOutlined />,
      label: '统计',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <AntHeader className="flex items-center justify-between">
      <div className="flex items-center">
        <ExperimentOutlined className="text-white text-2xl mr-3" />
        <Title level={3} className="text-white m-0">
          中国有机过氧化物数据库
        </Title>
      </div>
      <Menu
        theme="dark"
        mode="horizontal"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          backgroundColor: 'transparent',
          border: 'none',
          color: '#ffffff'
        }}
        className="custom-menu"
      />
    </AntHeader>
  );
};

export default Header;
