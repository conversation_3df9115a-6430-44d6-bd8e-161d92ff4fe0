.company-detail-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px;
  border-radius: 12px;
  margin-bottom: 24px;
  color: white;
  position: relative;
}

.back-button {
  position: absolute;
  top: 24px;
  left: 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.company-header {
  padding-left: 100px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 16px;
}

.company-title {
  flex: 1;
  min-width: 300px;
}

.company-name {
  color: white !important;
  margin-bottom: 8px !important;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 2.2rem;
}

.verified-icon {
  color: #52c41a;
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.company-name-en {
  font-size: 1.2rem;
  opacity: 0.9;
  font-style: italic;
  display: block;
  margin-top: 4px;
}

.company-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.company-tags .ant-tag {
  margin: 0;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
  color: white;
  backdrop-filter: blur(10px);
}

.scale-tag {
  background: rgba(255, 255, 255, 0.2) !important;
}

.type-tag {
  background: rgba(255, 255, 255, 0.2) !important;
}

.product-count-tag {
  background: rgba(24, 144, 255, 0.3) !important;
}

.info-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.info-card .ant-card-head {
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
  border-bottom: 1px solid #e8f4fd;
}

.info-card .ant-descriptions-item-label {
  font-weight: 600;
  color: #595959;
  width: 80px;
}

.info-card .ant-descriptions-item-content {
  color: #262626;
}

.company-description {
  margin-top: 8px;
  line-height: 1.6;
  color: #595959;
}

.products-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.products-card .ant-card-head {
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
  border-bottom: 1px solid #e8f4fd;
}

.cas-link {
  color: #1890ff;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.cas-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.product-name {
  font-size: 0.85rem;
  color: #8c8c8c;
  margin-top: 2px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  gap: 16px;
}

/* 表格样式优化 */
.products-card .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #e8f4fd;
}

.products-card .ant-table-tbody > tr:hover > td {
  background: #f0f9ff;
}

.products-card .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

/* Badge 样式 */
.ant-badge-status-dot {
  width: 8px;
  height: 8px;
}

.ant-badge-status-text {
  margin-left: 8px;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .company-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .company-title {
    min-width: auto;
    width: 100%;
  }
  
  .company-tags {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .company-detail-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .back-button {
    position: static;
    margin-bottom: 16px;
  }
  
  .company-header {
    padding-left: 0;
  }
  
  .company-name {
    font-size: 1.8rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .verified-icon {
    font-size: 20px;
  }
  
  .company-name-en {
    font-size: 1rem;
  }
  
  .company-tags .ant-tag {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
  
  .info-card .ant-descriptions-item-label {
    width: 70px;
    font-size: 0.9rem;
  }
  
  .products-card .ant-table {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .company-name {
    font-size: 1.5rem;
  }
  
  .company-tags {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .company-tags .ant-tag {
    margin-bottom: 4px;
  }
  
  .info-card .ant-descriptions {
    font-size: 0.9rem;
  }
  
  .info-card .ant-descriptions-item-label {
    width: 60px;
    font-size: 0.85rem;
  }
}

/* 动画效果 */
.info-card, .products-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 链接样式 */
.info-card a {
  color: #1890ff;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.info-card a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 分割线样式 */
.info-card .ant-divider {
  margin: 16px 0;
  border-color: #e8f4fd;
}
