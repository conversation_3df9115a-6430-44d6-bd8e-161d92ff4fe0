import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Typography,
  Input,
  Button,
  List,
  Tag,
  Statistic,
  Space
} from 'antd';
import {
  SearchOutlined,
  ExperimentOutlined,
  BarChartOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { RootState, AppDispatch } from '../store/store';
import { fetchChemicals, fetchStatistics } from '../store/slices/chemicalSlice';


const { Title, Paragraph } = Typography;
const { Search } = Input;

const HomePage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { compounds, statistics, loading } = useSelector((state: RootState) => state.chemicals);

  useEffect(() => {
    console.log('HomePage: 开始获取数据');
    dispatch(fetchChemicals({ page: 1 })).then((result) => {
      console.log('HomePage: fetchChemicals 结果', result);
    });
    dispatch(fetchStatistics()).then((result) => {
      console.log('HomePage: fetchStatistics 结果', result);
    });
  }, [dispatch]);

  // 调试信息
  console.log('HomePage render:', {
    compounds: compounds.length,
    statistics: !!statistics,
    loading,
    compoundsData: compounds,
    statisticsData: statistics
  });

  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/search?q=${encodeURIComponent(value.trim())}`);
    }
  };



  const handleChemicalClick = (casNumber: string) => {
    navigate(`/chemical/${casNumber}`);
  };



  return (
    <div className="container mx-auto px-4 py-8">
      {/* 欢迎区域 */}
      <div className="text-center mb-12">
        <Title level={1} className="mb-4">
          <ExperimentOutlined className="mr-3" />
          中国有机过氧化物数据库
        </Title>
        <Paragraph className="text-lg text-gray-600 mb-8">
          全球首个以CAS号为主键的有机过氧化物专业数据库
        </Paragraph>


        
        {/* 搜索框 */}
        <div className="max-w-2xl mx-auto">
          <Search
            placeholder="输入CAS号、化学品名称或分子式进行搜索"
            allowClear
            enterButton={<Button type="primary" icon={<SearchOutlined />}>搜索</Button>}
            size="large"
            onSearch={handleSearch}
          />
        </div>
      </div>

      {/* 统计信息 */}
      {statistics && (
        <Row gutter={[16, 16]} className="mb-12">
          <Col xs={24} sm={12} md={6}>
            <Card className="stats-card">
              <Statistic
                title="化学品总数"
                value={statistics.total_compounds}
                prefix={<ExperimentOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="stats-card">
              <Statistic
                title="高完整度数据"
                value={statistics.data_completeness?.high_completeness || 0}
                prefix={<SafetyOutlined />}
                valueStyle={{ color: '#52c41a' }}
                suffix="(≥80%)"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="stats-card">
              <Statistic
                title="数据完整率"
                value={statistics.data_completeness?.avg_completeness || 0}
                suffix="%"
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="stats-card">
              <Statistic
                title="企业数量"
                value={statistics.total_companies}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 最新化学品 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card 
            title="最新化学品"
            extra={<Button type="link" onClick={() => navigate('/search')}>查看更多</Button>}
            loading={loading}
          >
            {compounds.length === 0 && !loading && (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                暂无化学品数据
              </div>
            )}
            <List
              dataSource={compounds.slice(0, 5)}
              renderItem={(item) => (
                <List.Item 
                  className="chemical-card cursor-pointer"
                  onClick={() => handleChemicalClick(item.cas_number)}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{item.chinese_name || item.english_name || item.cas_number}</span>
                        
                        {item.data_completeness !== undefined && (
                          <Tag color={item.data_completeness > 80 ? 'green' : item.data_completeness > 50 ? 'orange' : 'red'}>
                            数据完整性: {item.data_completeness}%
                          </Tag>
                        )}
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size="small">
                        <span>CAS号: {item.cas_number}</span>
                        <span>分子式: {item.molecular_formula}</span>
                        {item.molecular_weight && (
                          <span>分子量: {item.molecular_weight}</span>
                        )}
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="快速导航">
            <Space direction="vertical" size="large" className="w-full">
              <Button 
                type="primary" 
                icon={<SearchOutlined />} 
                size="large" 
                block
                onClick={() => navigate('/search')}
              >
                高级搜索
              </Button>
              <Button 
                icon={<BarChartOutlined />} 
                size="large" 
                block
                onClick={() => navigate('/statistics')}
              >
                数据统计
              </Button>
              <Button 
                icon={<ExperimentOutlined />} 
                size="large" 
                block
                onClick={() => window.open('/api/docs/', '_blank')}
              >
                API文档
              </Button>
            </Space>
          </Card>

          {/* 数据完整性分布 */}
          {statistics?.data_completeness && (
            <Card title="数据完整性分布" className="mt-4">
              <Space direction="vertical" className="w-full">
                <div className="flex justify-between items-center">
                  <span>高完整度 (≥80%)</span>
                  <Tag color="green">{statistics.data_completeness?.high_completeness || 0}</Tag>
                </div>
                <div className="flex justify-between items-center">
                  <span>中等完整度 (50-79%)</span>
                  <Tag color="orange">{statistics.data_completeness?.medium_completeness || 0}</Tag>
                </div>
                <div className="flex justify-between items-center">
                  <span>低完整度 (&lt;50%)</span>
                  <Tag color="red">{statistics.data_completeness?.low_completeness || 0}</Tag>
                </div>
                <div className="flex justify-between items-center">
                  <span>平均完整度</span>
                  <Tag color="blue">{statistics.data_completeness?.avg_completeness || 0}%</Tag>
                </div>
              </Space>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default HomePage;
