import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  List,
  Tag,
  Space,
  Pagination,
  Empty,
  Spin,
  Row,
  Col,

} from 'antd';
import { SearchOutlined, ExperimentOutlined } from '@ant-design/icons';
import { RootState, AppDispatch } from '../store/store';
import { searchChemicals, fetchChemicals } from '../store/slices/chemicalSlice';

const { Option } = Select;

const SearchPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  
  const { compounds, searchResults, loading, pagination } = useSelector((state: RootState) => state.chemicals);
  const [currentPage, setCurrentPage] = useState(1);
  const [isSearchMode, setIsSearchMode] = useState(false);

  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      form.setFieldsValue({ query, search_type: 'all' });
      handleSearch({ query, search_type: 'all' });
    } else {
      // 默认加载所有化学品，使用默认的 page_size (20)
      dispatch(fetchChemicals({ page: 1 }));
    }
  }, [searchParams, dispatch, form]);

  const handleSearch = (values: any) => {
    // 如果没有输入关键词但选择了搜索类型，仍然可以搜索
    if (!values.query && values.search_type === 'all') {
      // 如果既没有关键词也没有特定类型，显示所有数据
      setIsSearchMode(false);
      dispatch(fetchChemicals({ page: 1 }));
      return;
    }

    setIsSearchMode(true);
    setCurrentPage(1);
    dispatch(searchChemicals(values));
  };

  const handleReset = () => {
    form.resetFields();
    setIsSearchMode(false);
    setCurrentPage(1);
    dispatch(fetchChemicals({ page: 1 }));
  };

  const handleChemicalClick = (casNumber: string) => {
    navigate(`/chemical/${casNumber}`);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (!isSearchMode) {
      dispatch(fetchChemicals({ page }));
    } else {
      // 保持搜索结果的分页状态
      // 这可能需要后端支持搜索分页
    }
  };

  const displayData = isSearchMode ? searchResults : compounds;

  return (
    <div className="container mx-auto px-4 py-8">
      <Card title="化学品搜索" className="search-form mb-6">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSearch}
          initialValues={{
            search_type: 'all'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Form.Item
                label="搜索关键词"
                name="query"
              >
                <Input
                  placeholder="输入CAS号、化学名称或分子式（可选）..."
                  prefix={<SearchOutlined />}
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={6}>
              <Form.Item label="搜索类型" name="search_type">
                <Select size="large">
                  <Option value="all">全部</Option>
                  <Option value="cas">CAS号</Option>
                  <Option value="name_cn">中文名</Option>
                  <Option value="name_en">英文名</Option>
                  <Option value="formula">分子式</Option>
                </Select>
              </Form.Item>
            </Col>
            
            <Col xs={24} md={4}>
              <Form.Item label="操作">
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />} size="large">
                    搜索
                  </Button>
                  <Button onClick={handleReset} size="large">
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
          

        </Form>
      </Card>

      <Card title={`搜索结果 (${isSearchMode ? displayData.length : pagination.total} 条)`}>
        <Spin spinning={loading}>
          {displayData.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无数据"
            />
          ) : (
            <>
              <List
                dataSource={displayData}
                renderItem={(item) => (
                  <List.Item 
                    className="chemical-card"
                    onClick={() => handleChemicalClick(item.cas_number)}
                  >
                    <List.Item.Meta
                      avatar={<ExperimentOutlined className="text-2xl text-blue-500" />}
                      title={
                        <Space>
                          <span className="text-lg font-semibold">
                            {item.chinese_name || item.english_name}
                          </span>
                          
                          {item.data_completeness !== undefined && (
                            <Tag color={item.data_completeness > 80 ? 'green' : item.data_completeness > 50 ? 'orange' : 'red'}>
                              数据完整性: {item.data_completeness}%
                            </Tag>
                          )}
                        </Space>
                      }
                      description={
                        <Space direction="vertical" size="small" className="w-full">
                          <div>
                            <strong>CAS号:</strong> {item.cas_number}
                          </div>
                          <div>
                            <strong>分子式:</strong> {item.molecular_formula}
                          </div>
                          {item.molecular_weight && (
                            <div>
                              <strong>分子量:</strong> {item.molecular_weight}
                            </div>
                          )}
                          {item.english_name && item.chinese_name && (
                            <div>
                              <strong>英文名:</strong> {item.english_name}
                            </div>
                          )}
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
              
              {!isSearchMode && (
                <div className="flex justify-center mt-6">
                  <Pagination
                    current={currentPage}
                    pageSize={20}
                    total={pagination.total}
                    onChange={handlePageChange}
                    showSizeChanger={false}
                    showQuickJumper
                    showTotal={(total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                  />
                </div>
              )}
            </>
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default SearchPage;
