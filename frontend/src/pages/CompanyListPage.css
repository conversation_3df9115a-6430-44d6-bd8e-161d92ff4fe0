.company-list-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 8px;
  color: white;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.statistics-row {
  margin-bottom: 24px;
}

.statistics-row .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.company-grid {
  margin-bottom: 32px;
}

.company-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.company-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.company-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.company-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #1890ff;
  flex: 1;
  line-height: 1.4;
}

.verified-icon {
  color: #52c41a;
  font-size: 16px;
  margin-left: 8px;
}

.company-name-en {
  font-size: 0.9rem;
  color: #666;
  margin: 4px 0 12px 0;
  font-style: italic;
}

.company-info {
  margin-bottom: 12px;
}

.company-info p {
  margin: 4px 0;
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
}

.company-tags {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.company-tags .ant-tag {
  margin: 0;
  border-radius: 4px;
  font-size: 0.8rem;
}

.company-website {
  margin-top: 8px;
}

.company-website a {
  color: #1890ff;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.company-website a:hover {
  text-decoration: underline;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-list-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .search-card .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-card .ant-col {
    width: 100% !important;
  }
  
  .statistics-row .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .page-header h1 {
    font-size: 1.8rem;
  }
  
  .company-name {
    font-size: 1rem;
  }
  
  .company-card .ant-card-body {
    padding: 16px;
  }
}

/* 加载状态 */
.ant-spin-container {
  min-height: 400px;
}

/* 空状态 */
.ant-empty {
  padding: 60px 0;
}

/* 统计卡片动画 */
.statistics-row .ant-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 企业卡片动画 */
.company-card {
  animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 搜索框样式 */
.search-card .ant-input-search .ant-input-group .ant-input-group-addon {
  background: #1890ff;
  border-color: #1890ff;
}

.search-card .ant-input-search .ant-input-group .ant-input-group-addon .ant-btn {
  background: transparent;
  border: none;
  color: white;
}

/* 选择器样式 */
.search-card .ant-select-selector {
  border-radius: 6px;
}

/* 按钮样式 */
.search-card .ant-btn {
  border-radius: 6px;
}
