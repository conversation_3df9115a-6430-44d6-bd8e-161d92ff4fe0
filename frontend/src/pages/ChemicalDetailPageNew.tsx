import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  Spin,
  Alert,
  Row,
  Col,
  Typography,
  List,
  Empty,
  Divider,
  Tooltip
} from 'antd';
import {
  ArrowLeftOutlined,
  ExperimentOutlined,
  SafetyOutlined,
  InfoCircleOutlined,
  ShopOutlined,
  TagsOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { RootState, AppDispatch } from '../store/store';
import { fetchChemicalDetail } from '../store/slices/chemicalSlice';

const { Title, Text } = Typography;

interface ChemicalData {
  cas_number: string;
  chinese_name: string;
  english_name: string;
  molecular_formula: string;
  molecular_weight: string;
  category: string;
  aliases: Array<{
    alias_name: string;
    language: string;
    alias_type: string;
    source: string;
  }>;
  chinese_aliases: string[];
  english_aliases: string[];
  company_products: Array<{
    id: number;
    company_short_name: string;
    company_name: string;
    full_model_name: string;
    assay: string;
    physical_form: string;
    status: string;
    applications: string;
  }>;
}

const ChemicalDetailPageNew: React.FC = () => {
  const { casNumber } = useParams<{ casNumber: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { currentCompound, loading, error } = useSelector((state: RootState) => state.chemicals);

  useEffect(() => {
    if (casNumber) {
      dispatch(fetchChemicalDetail(casNumber));
    }
  }, [dispatch, casNumber]);

  // 过滤有效别名（排除无效数据）
  const filterValidAliases = (aliases: string[]) => {
    return aliases.filter(alias => 
      alias && 
      alias.trim() !== '' && 
      alias !== '[]' && 
      alias !== 'Directory' &&
      !alias.includes('IdentificationChemical') &&
      alias.length < 100 // 排除过长的无效数据
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="错误"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => navigate(-1)}>
            返回
          </Button>
        }
      />
    );
  }

  if (!currentCompound) {
    return (
      <Empty
        description="暂无数据"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      >
        <Button type="primary" onClick={() => navigate(-1)}>
          返回
        </Button>
      </Empty>
    );
  }

  const compound = currentCompound as ChemicalData;
  const validChineseAliases = filterValidAliases(compound.chinese_aliases || []);
  const validEnglishAliases = filterValidAliases(compound.english_aliases || []);

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate(-1)}
          className="mb-4"
        >
          返回
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="mb-2">
              {compound.chinese_name || compound.english_name || compound.cas_number}
            </Title>
            <Space>
              <Tag color="blue">CAS: {compound.cas_number}</Tag>
              <Tag
                color={
                  compound.data_completeness >= 80 ? 'green' :
                  compound.data_completeness >= 50 ? 'orange' : 'red'
                }
                icon={<GlobalOutlined />}
              >
                完整度 {compound.data_completeness}%
              </Tag>
            </Space>
          </div>
        </div>
      </div>

      <Row gutter={[24, 24]}>
        {/* 基本信息 */}
        <Col xs={24} lg={12}>
          <Card title={<><InfoCircleOutlined className="mr-2" />基本信息</>}>
            <Descriptions column={1} bordered>
              <Descriptions.Item label="CAS号">
                {compound.cas_number}
              </Descriptions.Item>
              <Descriptions.Item label="中文名称">
                {compound.chinese_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="英文名称">
                {compound.english_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="分子式">
                {compound.molecular_formula || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="分子量">
                {compound.molecular_weight ? `${compound.molecular_weight} g/mol` : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 别名信息 */}
        <Col xs={24} lg={12}>
          <Card title={<><TagsOutlined className="mr-2" />别名信息</>}>
            <div className="space-y-4">
              {/* 中文别名 */}
              <div>
                <Text strong>中文别名:</Text>
                <div className="mt-2">
                  {validChineseAliases.length > 0 ? (
                    <Space wrap>
                      {validChineseAliases.map((alias, index) => (
                        <Tag key={index} color="blue">{alias}</Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary">暂无别名</Text>
                  )}
                </div>
              </div>

              <Divider />

              {/* 英文别名 */}
              <div>
                <Text strong>英文别名:</Text>
                <div className="mt-2">
                  {validEnglishAliases.length > 0 ? (
                    <Space wrap>
                      {validEnglishAliases.map((alias, index) => (
                        <Tag key={index} color="green">{alias}</Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary">暂无别名</Text>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </Col>

        {/* 工业用途 */}
        <Col xs={24}>
          <Card title={<><ExperimentOutlined className="mr-2" />工业用途</>}>
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Text strong>中文用途:</Text>
                <div className="mt-2 p-3 bg-gray-50 rounded">
                  {compound.applications_cn || '暂无用途信息'}
                </div>
              </Col>
              <Col xs={24} md={12}>
                <Text strong>English Applications:</Text>
                <div className="mt-2 p-3 bg-gray-50 rounded">
                  {compound.applications_en || 'No application information available'}
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 供应商信息 */}
        {compound.company_products && compound.company_products.length > 0 && (
          <Col xs={24}>
            <Card title={<><ShopOutlined className="mr-2" />企业产品 ({compound.company_products.length})</>}>
              <List
                dataSource={compound.company_products}
                renderItem={(product, index) => (
                  <List.Item key={index}>
                    <List.Item.Meta
                      title={
                        <Space>
                          <Tag color="blue">{product.company_short_name}</Tag>
                          <Text strong>{product.full_model_name}</Text>
                          {product.status === '停产' && (
                            <Tag color="red">停产</Tag>
                          )}
                        </Space>
                      }
                      description={
                        <div>
                          <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            <Row gutter={[16, 8]}>
                              <Col span={6}>
                                <Text strong>企业:</Text> {product.company_name}
                              </Col>
                              {product.assay && (
                                <Col span={6}>
                                  <Text strong>含量:</Text> {product.assay}
                                </Col>
                              )}
                              {product.physical_form && (
                                <Col span={6}>
                                  <Text strong>物理形态:</Text> {product.physical_form}
                                </Col>
                              )}
                              {product.status && (
                                <Col span={6}>
                                  <Text strong>状态:</Text> {product.status}
                                </Col>
                              )}
                            </Row>
                            {product.applications && (
                              <div>
                                <Text strong>应用:</Text>
                                <div style={{ marginTop: 4, padding: '8px 12px', background: '#f5f5f5', borderRadius: '4px' }}>
                                  {product.applications}
                                </div>
                              </div>
                            )}
                          </Space>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default ChemicalDetailPageNew;
