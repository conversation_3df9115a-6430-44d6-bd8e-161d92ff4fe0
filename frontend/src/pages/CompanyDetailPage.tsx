import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { 
  Card, Row, Col, Tag, Button, Table, Spin, Empty, Descriptions, 
  Typography, Space, Divider, Badge, Tooltip 
} from 'antd';
import { 
  ArrowLeftOutlined, ShopOutlined, GlobalOutlined, MailOutlined, 
  PhoneOutlined, SafetyCertificateOutlined, ProductOutlined,
  ExperimentOutlined, LinkOutlined
} from '@ant-design/icons';
import './CompanyDetailPage.css';

const { Title, Paragraph, Text } = Typography;

interface CompanyProduct {
  id: number;
  chemical_cas: string;
  brand: string;
  model: string;
  full_model_name: string;
  assay: string;
  physical_form: string;
  lifecycle_status: string;
}

interface Company {
  id: number;
  name: string;
  name_en: string;
  short_name: string;
  province?: string;
  city?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  description_cn?: string;
  description_en?: string;
  business_type: string;
  company_scale: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  products: CompanyProduct[];
  product_count: number;
}

const CompanyDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCompanyDetail();
  }, [id]);

  const fetchCompanyDetail = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/companies/${id}/`);
      const data = await response.json();
      setCompany(data);
    } catch (error) {
      console.error('获取企业详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getBusinessTypeText = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'manufacturer': '制造商',
      'distributor': '分销商',
      'trader': '贸易商',
      'agent': '代理商',
      'other': '其他'
    };
    return typeMap[type] || type;
  };

  const getScaleText = (scale: string) => {
    const scaleMap: { [key: string]: string } = {
      '跨国/全球企业': '跨国/全球企业',
      'large': '大型企业',
      'medium': '中型企业',
      'small': '小型企业',
      'startup': '初创企业',
      'unknown': '未知'
    };
    return scaleMap[scale] || scale;
  };

  const getScaleColor = (scale: string) => {
    const colorMap: { [key: string]: string } = {
      '跨国/全球企业': 'purple',
      'large': 'red',
      'medium': 'orange',
      'small': 'blue',
      'startup': 'green',
      'unknown': 'default'
    };
    return colorMap[scale] || 'default';
  };

  const getLifecycleStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge status="success" text="在产" />;
      case 'discontinued':
        return <Badge status="default" text="停产" />;
      case 'stopped_selling':
        return <Badge status="warning" text="停售" />;
      case 'limited_production':
        return <Badge status="warning" text="限产" />;
      case 'trial_production':
        return <Badge status="processing" text="试产" />;
      case 'in_development':
        return <Badge status="processing" text="研发中" />;
      case 'planned_launch':
        return <Badge status="processing" text="计划上市" />;
      case 'being_replaced':
        return <Badge status="warning" text="替代中" />;
      case 'phased_out':
        return <Badge status="error" text="淘汰" />;
      case 'imported':
        return <Badge status="success" text="进口" />;
      case 'unknown':
        return <Badge status="default" text="未知" />;
      default:
        return <Badge status="default" text={status || "未知"} />;
    }
  };

  const productColumns = [
    {
      title: 'CAS号',
      dataIndex: 'chemical_cas',
      key: 'chemical_cas',
      render: (cas: string) => (
        <Link to={`/chemical/${cas}`} className="cas-link">
          <ExperimentOutlined /> {cas}
        </Link>
      ),
    },
    {
      title: '产品型号',
      dataIndex: 'full_model_name',
      key: 'full_model_name',
      render: (modelName: string) => (
        <Text strong>{modelName}</Text>
      ),
    },
    {
      title: '含量',
      dataIndex: 'assay',
      key: 'assay',
      render: (assay: string) => assay || '-',
    },
    {
      title: '物理形态',
      dataIndex: 'physical_form',
      key: 'physical_form',
      render: (form: string) => form || '-',
    },
    {
      title: '生命周期状态',
      key: 'lifecycle_status',
      render: (record: CompanyProduct) =>
        getLifecycleStatusBadge(record.lifecycle_status),
    },
  ];

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  if (!company) {
    return (
      <div className="error-container">
        <Empty description="企业信息不存在" />
        <Button type="primary" onClick={() => window.history.back()}>
          返回
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 返回按钮 */}
      <div className="mb-6">
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => window.history.back()}
          size="large"
        >
          返回
        </Button>
      </div>

      {/* 企业信息头部 */}
      <div className="text-center mb-12">
        <Title level={1} className="mb-4">
          <ShopOutlined className="mr-3" />
          {company.name}
        </Title>
        {company.name_en && (
          <Paragraph className="text-lg text-gray-600 mb-6">
            {company.name_en}
          </Paragraph>
        )}

        <Space size="large" wrap>
          <Tag color={getScaleColor(company.company_scale)} className="text-base px-3 py-1">
            {getScaleText(company.company_scale)}
          </Tag>
          <Tag color="blue" className="text-base px-3 py-1">
            {getBusinessTypeText(company.business_type)}
          </Tag>
          <Tag color="green" className="text-base px-3 py-1">
            <ProductOutlined /> {company.products?.length || 0} 个产品
          </Tag>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* 左侧：企业信息 */}
        <Col xs={24} lg={8}>
          <Card title="企业信息" className="mb-6">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="企业全称">
                {company.name}
              </Descriptions.Item>
              
              {company.name_en && (
                <Descriptions.Item label="英文名称">
                  {company.name_en}
                </Descriptions.Item>
              )}
              
              {company.short_name && (
                <Descriptions.Item label="企业缩写">
                  <Tag color="blue">{company.short_name}</Tag>
                </Descriptions.Item>
              )}
              
              {(company.province || company.city) && (
                <Descriptions.Item label="所在地区">
                  <Space>
                    <GlobalOutlined />
                    {company.province}
                    {company.city && company.province && ` · ${company.city}`}
                    {company.city && !company.province && company.city}
                  </Space>
                </Descriptions.Item>
              )}
              
              <Descriptions.Item label="企业类型">
                {getBusinessTypeText(company.business_type)}
              </Descriptions.Item>
              
              <Descriptions.Item label="企业规模">
                <Tag color={getScaleColor(company.company_scale)}>
                  {getScaleText(company.company_scale)}
                </Tag>
              </Descriptions.Item>
              
              {company.website && (
                <Descriptions.Item label="官方网站">
                  <a href={company.website} target="_blank" rel="noopener noreferrer">
                    <LinkOutlined /> 访问官网
                  </a>
                </Descriptions.Item>
              )}
              
              {company.contact_email && (
                <Descriptions.Item label="联系邮箱">
                  <a href={`mailto:${company.contact_email}`}>
                    <MailOutlined /> {company.contact_email}
                  </a>
                </Descriptions.Item>
              )}
              
              {company.contact_phone && (
                <Descriptions.Item label="联系电话">
                  <PhoneOutlined /> {company.contact_phone}
                </Descriptions.Item>
              )}
            </Descriptions>
            
            {company.description_cn && (
              <>
                <Divider />
                <div>
                  <Text strong>企业简介</Text>
                  <Paragraph className="company-description">
                    {company.description_cn}
                  </Paragraph>
                </div>
              </>
            )}
          </Card>
        </Col>

        {/* 右侧：产品列表 */}
        <Col xs={24} lg={16}>
          <Card
            title={`产品列表 (${company.products?.length || 0})`}
            className="mb-6"
          >
            {!company.products || company.products.length === 0 ? (
              <Empty description="暂无产品信息" />
            ) : (
              <Table
                columns={productColumns}
                dataSource={company.products}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 个产品`,
                }}
                scroll={{ x: 800 }}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CompanyDetailPage;
