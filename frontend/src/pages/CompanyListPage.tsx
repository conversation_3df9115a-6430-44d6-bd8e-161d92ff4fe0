import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Input, Select, Button, Tag, Spin, Empty, Pagination, Space, Statistic, Table, Typography } from 'antd';
import { SearchOutlined, ShopOutlined, GlobalOutlined, TeamOutlined, EnvironmentOutlined, LinkOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import './CompanyListPage.css';

const { Search } = Input;
const { Option } = Select;
const { Title, Paragraph } = Typography;

interface Company {
  id: number;
  name: string;
  name_en: string;
  short_name: string;
  english_short_name: string;
  province?: string;
  city?: string;
  website?: string;
  business_type: string;
  company_scale: string;
  is_verified: boolean;
}

interface CompanyListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Company[];
}

interface Statistics {
  company_statistics: {
    total_companies: number;
    verified_companies: number;
    verification_rate: number;
    province_distribution: Array<{ province: string; count: number }>;
    type_distribution: Array<{ business_type: string; count: number }>;
  };
  product_statistics: {
    total_products: number;
    active_products: number;
  };
}

const CompanyListPage: React.FC = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [statistics, setStatistics] = useState<Statistics | null>(null);

  const pageSize = 12;

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/companies/statistics/');
      const data = await response.json();
      setStatistics(data);
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  // 获取企业列表
  const fetchCompanies = async (page = 1, query = '', businessType = '') => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
      });

      if (query) params.append('search', query);
      if (businessType) params.append('business_type', businessType);

      const response = await fetch(`http://localhost:8000/api/companies/?${params}`);
      const data: CompanyListResponse = await response.json();
      
      setCompanies(data.results);
      setTotal(data.count);
    } catch (error) {
      console.error('获取企业列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
    fetchCompanies(currentPage, searchQuery, selectedType);
  }, [currentPage, searchQuery, selectedType]);

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };



  const handleTypeChange = (value: string) => {
    setSelectedType(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getBusinessTypeText = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'manufacturer': '制造商',
      'distributor': '分销商',
      'trader': '贸易商',
      'agent': '代理商',
      'other': '其他'
    };
    return typeMap[type] || type;
  };

  const getScaleText = (scale: string) => {
    const scaleMap: { [key: string]: string } = {
      '跨国/全球企业': '跨国/全球企业',
      'large': '大型企业',
      'medium': '中型企业',
      'small': '小型企业',
      'startup': '初创企业',
      'unknown': '未知'
    };
    return scaleMap[scale] || scale;
  };

  const getScaleColor = (scale: string) => {
    const colorMap: { [key: string]: string } = {
      '跨国/全球企业': 'purple',
      'large': 'red',
      'medium': 'orange',
      'small': 'blue',
      'startup': 'green',
      'unknown': 'default'
    };
    return colorMap[scale] || 'default';
  };

  // 表格列定义
  const columns = [
    {
      title: '企业名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Company) => (
        <Link to={`/companies/${record.english_short_name || record.id}`}>
          <strong>{text}</strong>
        </Link>
      ),
    },
    {
      title: '简称',
      dataIndex: 'short_name',
      key: 'short_name',
    },
    {
      title: '地区',
      key: 'location',
      render: (record: Company) => (
        <span>
          {record.province && <span><EnvironmentOutlined /> {record.province}</span>}
          {record.city && record.province && ' - '}
          {record.city}
        </span>
      ),
    },
    {
      title: '企业类型',
      dataIndex: 'business_type',
      key: 'business_type',
      render: (type: string) => (
        <Tag>{getBusinessTypeText(type)}</Tag>
      ),
    },
    {
      title: '企业规模',
      dataIndex: 'company_scale',
      key: 'company_scale',
      render: (scale: string) => (
        <Tag color={getScaleColor(scale)}>
          {getScaleText(scale)}
        </Tag>
      ),
    },
    {
      title: '官网',
      dataIndex: 'website',
      key: 'website',
      render: (website: string) => (
        website ? (
          <a href={website} target="_blank" rel="noopener noreferrer">
            <LinkOutlined /> 访问
          </a>
        ) : '-'
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: Company) => (
        <Link to={`/companies/${record.english_short_name || record.id}`}>
          查看详情
        </Link>
      ),
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <Title level={1} className="mb-4">
          <ShopOutlined className="mr-3" />
          企业供应商
        </Title>
        <Paragraph className="text-lg text-gray-600 mb-8">
          有机过氧化物供应商企业目录
        </Paragraph>
      </div>

      {/* 统计信息 */}
      {statistics && (
        <Row gutter={[16, 16]} className="mb-12">
          <Col xs={24} sm={12} md={8}>
            <Card className="stats-card">
              <Statistic
                title="企业总数"
                value={statistics.company_statistics.total_companies}
                prefix={<ShopOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card className="stats-card">
              <Statistic
                title="产品总数"
                value={statistics.product_statistics.total_products}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card className="stats-card">
              <Statistic
                title="在产产品"
                value={statistics.product_statistics.active_products}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} md={12}>
            <Search
              placeholder="搜索企业名称..."
              allowClear
              enterButton={<Button type="primary" icon={<SearchOutlined />}>搜索</Button>}
              size="large"
              onSearch={handleSearch}
            />
          </Col>
          <Col xs={24} md={6}>
            <Select
              placeholder="企业类型"
              allowClear
              style={{ width: '100%' }}
              size="large"
              onChange={handleTypeChange}
            >
              {statistics?.company_statistics.type_distribution.map(item => (
                <Option key={item.business_type} value={item.business_type}>
                  {getBusinessTypeText(item.business_type)} ({item.count})
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} md={6}>
            <Button
              size="large"
              onClick={() => {
                setSearchQuery('');
                setSelectedType('');
                setCurrentPage(1);
              }}
            >
              重置筛选
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 企业列表 */}
      <Card className="mb-6">
        <Table
          columns={columns}
          dataSource={companies}
          loading={loading}
          rowKey="id"
          pagination={{
            current: currentPage,
            total: total,
            pageSize: pageSize,
            onChange: handlePageChange,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条企业`,
          }}
          locale={{
            emptyText: <Empty description="暂无企业数据" />
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default CompanyListPage;
