import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Spin,
  Tabs
} from 'antd';
import {
  BarChartOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  TrophyOutlined,
  ShopOutlined,
  GlobalOutlined,
  TagsOutlined
} from '@ant-design/icons';
import { RootState, AppDispatch } from '../store/store';
import { fetchStatistics } from '../store/slices/chemicalSlice';
import { fetchCompanyStatistics } from '../store/slices/companySlice';

const { Title } = Typography;
const { TabPane } = Tabs;

const StatisticsPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { statistics, loading } = useSelector((state: RootState) => state.chemicals);
  const { companyStatistics, loading: companyLoading } = useSelector((state: RootState) => state.companies);

  useEffect(() => {
    dispatch(fetchStatistics());
    dispatch(fetchCompanyStatistics());
  }, [dispatch]);

  if (loading || companyLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Title level={2}>
          <BarChartOutlined className="mr-3" />
          数据统计
        </Title>
      </div>

      <Tabs defaultActiveKey="chemicals" size="large">
        <TabPane tab={<span><ExperimentOutlined />化学品统计</span>} key="chemicals">
          {statistics && (
            <>
              {/* 化学品总体统计 */}
              <Row gutter={[16, 16]} className="mb-8">
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="化学品总数"
                      value={statistics.total_compounds}
                      prefix={<ExperimentOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="中文数据"
                      value={statistics.compounds_with_chinese}
                      prefix={<DatabaseOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="英文数据"
                      value={statistics.compounds_with_english}
                      prefix={<GlobalOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="高完整度数据"
                      value={statistics.data_completeness?.high_completeness || 0}
                      prefix={<TrophyOutlined />}
                      valueStyle={{ color: '#722ed1' }}
                      suffix="(≥80%)"
                    />
                  </Card>
                </Col>
              </Row>

              {/* 别名统计 */}
              <Row gutter={[16, 16]} className="mb-8">
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="别名总数"
                      value={statistics.total_aliases}
                      prefix={<TagsOutlined />}
                      valueStyle={{ color: '#fa8c16' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="中文别名"
                      value={statistics.chinese_aliases}
                      prefix={<TagsOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="英文别名"
                      value={statistics.english_aliases}
                      prefix={<TagsOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="平均别名数"
                      value={statistics.avg_aliases_per_compound}
                      prefix={<TagsOutlined />}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              </Row>
            </>
          )}
        </TabPane>

        <TabPane tab={<span><ShopOutlined />企业统计</span>} key="companies">
          {companyStatistics && (
            <>
              {/* 企业总体统计 */}
              <Row gutter={[16, 16]} className="mb-8">
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="企业总数"
                      value={companyStatistics.company_statistics.total_companies}
                      prefix={<ShopOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="已验证企业"
                      value={companyStatistics.company_statistics.verified_companies}
                      prefix={<TrophyOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="产品总数"
                      value={companyStatistics.product_statistics.total_products}
                      prefix={<ExperimentOutlined />}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card className="stats-card">
                    <Statistic
                      title="产品覆盖率"
                      value={statistics?.product_coverage_rate || 0}
                      suffix="%"
                      prefix={<BarChartOutlined />}
                      valueStyle={{ color: '#fa8c16' }}
                    />
                  </Card>
                </Col>
              </Row>
            </>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default StatisticsPage;
