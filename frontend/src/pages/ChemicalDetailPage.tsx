import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import {
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  Spin,
  Alert,
  Row,
  Col,
  Typography,
  Collapse,
  Table
} from 'antd';
import {
  ArrowLeftOutlined,
  ExperimentOutlined,
  SafetyOutlined,
  InfoCircleOutlined,
  ShopOutlined,
  TagsOutlined
} from '@ant-design/icons';
import { RootState, AppDispatch } from '../store/store';
import { fetchChemicalDetail } from '../store/slices/chemicalSlice';
import { fetchSuppliersByCas } from '../store/slices/companySlice';

const { Title } = Typography;
const { Panel } = Collapse;

const ChemicalDetailPage: React.FC = () => {
  const { casNumber } = useParams<{ casNumber: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const { currentCompound, loading, error } = useSelector((state: RootState) => state.chemicals);
  const { suppliers, loading: suppliersLoading } = useSelector((state: RootState) => state.companies);

  useEffect(() => {
    if (casNumber) {
      dispatch(fetchChemicalDetail(casNumber));
      dispatch(fetchSuppliersByCas(casNumber));
    }
  }, [casNumber, dispatch]);

  // 供应商表格列定义
  const supplierColumns = [
    {
      title: '企业名称',
      dataIndex: ['company', 'name'],
      key: 'company_name',
      render: (text: string, record: any) => (
        <div>
          <div className="font-semibold">{text}</div>
          {record.company.name_en && (
            <div className="text-sm text-gray-500">{record.company.name_en}</div>
          )}
        </div>
      ),
    },
    {
      title: '产品名称',
      key: 'product_name',
      render: (record: any) => (
        <div>
          {record.product_name_cn && <div>{record.product_name_cn}</div>}
          {record.product_name_en && (
            <div className="text-sm text-gray-500">{record.product_name_en}</div>
          )}
        </div>
      ),
    },
    {
      title: '纯度',
      dataIndex: 'purity',
      key: 'purity',
    },
    {
      title: '包装规格',
      dataIndex: 'package_size',
      key: 'package_size',
    },
    {
      title: '供货状态',
      key: 'availability',
      render: (record: any) => (
        <Tag color={record.is_available ? 'green' : 'red'}>
          {record.is_available ? '有货' : '缺货'}
        </Tag>
      ),
    },
    {
      title: '国家',
      dataIndex: ['company', 'country'],
      key: 'country',
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => navigate(-1)}>
              返回
            </Button>
          }
        />
      </div>
    );
  }

  if (!currentCompound) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert
          message="未找到化学品信息"
          description="请检查CAS号是否正确"
          type="warning"
          showIcon
          action={
            <Button size="small" onClick={() => navigate(-1)}>
              返回
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate(-1)}
          className="mb-4"
        >
          返回
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="mb-2">
              <ExperimentOutlined className="mr-2" />
              {currentCompound.chinese_name || currentCompound.english_name}
            </Title>
            <Space>
              <Tag color="blue" className="text-base px-3 py-1">
                CAS: {currentCompound.cas_number}
              </Tag>
              {currentCompound.chinese_name && (
                <Tag color="green" className="text-base px-3 py-1">
                  中文数据
                </Tag>
              )}
              {currentCompound.english_name && (
                <Tag color="blue" className="text-base px-3 py-1">
                  英文数据
                </Tag>
              )}
            </Space>
          </div>
        </div>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card title={<><InfoCircleOutlined className="mr-2" />基本信息</>} className="mb-6">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="中文名称">
                {currentCompound.chinese_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="英文名称">
                {currentCompound.english_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="CAS号">
                {currentCompound.cas_number}
              </Descriptions.Item>
              <Descriptions.Item label="分子式">
                {currentCompound.molecular_formula || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="分子量">
                {currentCompound.molecular_weight ? `${currentCompound.molecular_weight} g/mol` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="所属类别">
                {currentCompound.category || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Card title={<><ExperimentOutlined className="mr-2" />物理性质</>}>
            <Descriptions column={1} bordered>
              <Descriptions.Item label="熔点">
                {currentCompound.melting_point || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="沸点">
                {currentCompound.boiling_point || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="密度">
                {currentCompound.density || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="闪点">
                {currentCompound.flash_point || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="蒸气压">
                {currentCompound.vapor_pressure || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="折射率">
                {currentCompound.refractive_index || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="外观性状">
                {currentCompound.appearance_state || currentCompound.appearance || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="水溶解性">
                {currentCompound.water_solubility || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title={<><SafetyOutlined className="mr-2" />安全信息</>} className="mb-6">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="危险性符号(GHS)">
                {currentCompound.hazard_symbols_ghs || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="警示词">
                {currentCompound.warning_words || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="危险性描述">
                {currentCompound.hazard_description || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="防范说明">
                {currentCompound.prevention_statement || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="危险品标志">
                {currentCompound.hazard_symbols || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="危险类别码">
                {currentCompound.risk_codes || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="安全说明">
                {currentCompound.safety_statements || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="危险品运输编号">
                {currentCompound.transport_number || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="危险等级">
                {currentCompound.hazard_class || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="包装类别">
                {currentCompound.packaging_group || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Card title="应用信息">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="用途">
                {currentCompound.applications || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="储存条件">
                {currentCompound.storage_conditions || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* 别名信息 */}
      {((currentCompound.chinese_aliases && currentCompound.chinese_aliases.length > 0) ||
        (currentCompound.english_aliases && currentCompound.english_aliases.length > 0)) && (
        <Card title={<><TagsOutlined className="mr-2" />别名信息</>} className="mt-6">
          <Collapse>
            {currentCompound.chinese_aliases && currentCompound.chinese_aliases.length > 0 && (
              <Panel header={`中文别名 (${currentCompound.chinese_aliases.length})`} key="chinese">
                <div className="flex flex-wrap gap-2">
                  {currentCompound.chinese_aliases.map((alias: string, index: number) => (
                    <Tag key={index} color="green">{alias}</Tag>
                  ))}
                </div>
              </Panel>
            )}
            {currentCompound.english_aliases && currentCompound.english_aliases.length > 0 && (
              <Panel header={`英文别名 (${currentCompound.english_aliases.length})`} key="english">
                <div className="flex flex-wrap gap-2">
                  {currentCompound.english_aliases.map((alias: string, index: number) => (
                    <Tag key={index} color="blue">{alias}</Tag>
                  ))}
                </div>
              </Panel>
            )}
          </Collapse>
        </Card>
      )}

      {/* 供应商信息 */}
      <Card
        title={<><ShopOutlined className="mr-2" />供应商信息 ({suppliers.length})</>}
        className="mt-6"
        loading={suppliersLoading}
      >
        {suppliers.length > 0 ? (
          <Table
            dataSource={suppliers}
            columns={supplierColumns}
            pagination={false}
            size="small"
            rowKey={(record) => `${record.company.name}-${record.product_code || 'default'}`}
          />
        ) : (
          <div className="text-center text-gray-500 py-8">
            暂无供应商信息
          </div>
        )}
      </Card>

      {/* 更新时间信息 */}
      <Card className="mt-6">
        <div className="text-center text-gray-500">
          <p>
            创建时间: {currentCompound.created_at ? new Date(currentCompound.created_at).toLocaleString('zh-CN') : '-'}
            {currentCompound.updated_at && (
              <span className="ml-4">
                更新时间: {new Date(currentCompound.updated_at).toLocaleString('zh-CN')}
              </span>
            )}
          </p>
        </div>
      </Card>
    </div>
  );
};

export default ChemicalDetailPage;