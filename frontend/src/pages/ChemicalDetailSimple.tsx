import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Descriptions,
  Button,
  Spin,
  Empty,
  Typography,
  Tag,
  Space,
  Row,
  Col,
  Alert,
  List
} from 'antd';
import {
  ArrowLeftOutlined,
  InfoCircleOutlined,
  ExperimentOutlined,
  SafetyOutlined,
  GlobalOutlined,
  ShopOutlined
} from '@ant-design/icons';
import { RootState, AppDispatch } from '../store/store';
import { fetchChemicalDetail } from '../store/slices/chemicalSlice';

const { Title } = Typography;

const ChemicalDetailSimple: React.FC = () => {
  const { casNumber } = useParams<{ casNumber: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { currentCompound, loading, error } = useSelector((state: RootState) => state.chemicals);

  // 辅助函数：创建只包含有值字段的Descriptions项目
  const createDescriptionItems = (fields: Array<{label: string, value: any, render?: (value: any) => React.ReactNode}>) => {
    return fields
      .filter(field => {
        // 更严格的过滤条件
        if (!field.value) return false;
        if (typeof field.value === 'string' && field.value.trim() === '') return false;
        if (field.value === null || field.value === undefined) return false;
        return true;
      })
      .map((field, index) => (
        <Descriptions.Item key={index} label={field.label}>
          {field.render ? field.render(field.value) : field.value}
        </Descriptions.Item>
      ));
  };

  useEffect(() => {
    if (casNumber) {
      console.log('获取化学品详情:', casNumber);
      dispatch(fetchChemicalDetail(casNumber));
    }
  }, [dispatch, casNumber]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          action={
            <Button type="primary" onClick={() => navigate(-1)}>
              返回
            </Button>
          }
        />
      </div>
    );
  }

  if (!currentCompound) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Empty
          description="未找到化学品信息"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={() => navigate(-1)}>
            返回
          </Button>
        </Empty>
      </div>
    );
  }

  const compound = currentCompound;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate(-1)}
          className="mb-4"
        >
          返回
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="mb-2">
              {compound.chinese_name || compound.english_name || compound.cas_number}
            </Title>
            <Space>
              <Tag color="blue">CAS: {compound.cas_number}</Tag>
              <Tag
                color={
                  compound.data_completeness >= 80 ? 'green' :
                  compound.data_completeness >= 50 ? 'orange' : 'red'
                }
                icon={<GlobalOutlined />}
              >
                完整度 {compound.data_completeness}%
              </Tag>

            </Space>
          </div>
        </div>
      </div>

      {/* 瀑布流布局容器 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '20px',
        alignItems: 'start'
      }}>
        {/* 左列 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>

          {/* 别名信息 */}
          {(compound.chinese_aliases?.length > 0 || compound.english_aliases?.length > 0) && (
            <Card title="别名信息">
              <Row gutter={[16, 16]}>
                {compound.chinese_aliases?.length > 0 && (
                  <Col xs={24}>
                    <div>
                      <h4 className="mb-2">中文别名</h4>
                      <div className="space-y-1">
                        {compound.chinese_aliases.map((alias, index) => (
                          <Tag key={index} color="blue">{alias}</Tag>
                        ))}
                      </div>
                    </div>
                  </Col>
                )}
                {compound.english_aliases?.length > 0 && (
                  <Col xs={24}>
                    <div>
                      <h4 className="mb-2">英文别名</h4>
                      <div className="space-y-1">
                        {compound.english_aliases.map((alias, index) => (
                          <Tag key={index} color="green">{alias}</Tag>
                        ))}
                      </div>
                    </div>
                  </Col>
                )}
              </Row>
            </Card>
          )}

          {/* 基本信息 */}
          <Card title={<><InfoCircleOutlined className="mr-2" />基本信息</>}>
          <Descriptions column={1} bordered size="small">
            {createDescriptionItems([
              {
                label: 'CAS号',
                value: compound.cas_number,
                render: (value) => (
                  <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                    {value}
                  </span>
                )
              },
              {
                label: '中文名称',
                value: compound.chinese_name,
                render: (value) => (
                  <span style={{ fontWeight: 'bold' }}>
                    {value}
                  </span>
                )
              },
              {
                label: '英文名称',
                value: compound.english_name,
                render: (value) => (
                  <span style={{ fontWeight: 'bold' }}>
                    {value}
                  </span>
                )
              },
              {
                label: '分子式',
                value: compound.molecular_formula,
                render: (value) => (
                  <span style={{ fontFamily: 'monospace', fontSize: '14px' }}>
                    {value}
                  </span>
                )
              },
              {
                label: '分子量',
                value: compound.molecular_weight
              },
              {
                label: 'EINECS号',
                value: compound.einecs_number
              },
              {
                label: 'MDL号',
                value: compound.mdl_number,
                render: (value) => (
                  <span style={{ fontFamily: 'monospace' }}>
                    {value}
                  </span>
                )
              }
            ])}
          </Descriptions>
        </Card>

        {/* 物理化学性质 */}
        <Card title={<><ExperimentOutlined className="mr-2" />物理化学性质</>}>
            {(() => {
              const allPhysicalItems = createDescriptionItems([
                // 外观和形态
                {
                  label: '外观性质',
                  value: compound.appearance,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word', lineHeight: '1.4' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '外观性状',
                  value: compound.appearance_state,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '形态',
                  value: compound.form
                },
                {
                  label: '颜色',
                  value: compound.color
                },
                {
                  label: '气味',
                  value: compound.odor,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                // 物理常数
                {
                  label: '熔点',
                  value: compound.melting_point,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '沸点',
                  value: compound.boiling_point,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '密度',
                  value: compound.density,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '蒸气密度',
                  value: compound.vapor_density,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '蒸气压',
                  value: compound.vapor_pressure,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '折射率',
                  value: compound.refractive_index,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                // 溶解性和储存
                {
                  label: '溶解性',
                  value: compound.solubility,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '溶解度',
                  value: compound.solubility_detail,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '水溶解性',
                  value: compound.water_solubility,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '储存条件',
                  value: compound.storage_conditions,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                // 数据库引用
                {
                  label: 'BRN',
                  value: compound.brn,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: 'InChI Key',
                  value: compound.inchi_key,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-all', fontFamily: 'monospace', fontSize: '12px' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: 'LogP',
                  value: compound.log_p,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: 'CAS数据库',
                  value: compound.cas_database,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: 'IARC致癌物分类',
                  value: compound.iarc_classification,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: 'EPA化学物质信息',
                  value: compound.epa_info,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                }
              ]);

              return allPhysicalItems.length > 0 ? (
                <Descriptions column={1} bordered size="small">
                  {allPhysicalItems}
                </Descriptions>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  暂无物理化学性质数据
                </div>
              );
            })()}
        </Card>

        {/* 安全信息 */}
        <Card title={<><SafetyOutlined className="mr-2" />安全信息</>}>
            {(() => {
              const allSafetyItems = createDescriptionItems([
                {
                  label: '危险性符号(GHS)',
                  value: compound.hazard_symbols_ghs,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace', color: '#ff4d4f' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '警示词',
                  value: compound.warning_words,
                  render: (value) => (
                    <span style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '危险性描述',
                  value: compound.hazard_description,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word', fontFamily: 'monospace', fontSize: '12px' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '防范说明',
                  value: compound.prevention_statement,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word', fontFamily: 'monospace', fontSize: '12px' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '危险品标志',
                  value: compound.hazard_symbols,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace', color: '#ff4d4f' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '危险类别码',
                  value: compound.risk_codes,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word', fontFamily: 'monospace', fontSize: '12px' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '安全说明',
                  value: compound.safety_statements,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word', fontFamily: 'monospace', fontSize: '12px' }}>
                      {value}
                    </div>
                  )
                },
                {
                  label: '危险品运输编号',
                  value: compound.transport_number,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: 'WGK Germany',
                  value: compound.wgk_germany,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: 'RTECS号',
                  value: compound.rtecs_number,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '自燃温度',
                  value: compound.auto_ignition_temp,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: 'TSCA',
                  value: compound.tsca,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '危险等级',
                  value: compound.hazard_class,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '包装类别',
                  value: compound.packaging_group,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '海关编码',
                  value: compound.customs_code,
                  render: (value) => (
                    <span style={{ fontFamily: 'monospace' }}>
                      {value}
                    </span>
                  )
                },
                {
                  label: '毒害物质数据',
                  value: compound.hazardous_substances_data,
                  render: (value) => (
                    <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
                      {value}
                    </div>
                  )
                }
              ]);

              return allSafetyItems.length > 0 ? (
                <Descriptions column={1} bordered size="small">
                  {allSafetyItems}
                </Descriptions>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  暂无安全信息数据
                </div>
              );
            })()}
          </Card>

        </div>

        {/* 右列 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>

          {/* 企业产品信息 */}
          {currentCompound.company_products && currentCompound.company_products.length > 0 && (
            <Card
              title={
                <Space>
                  <ShopOutlined />
                  企业产品 ({currentCompound.company_products.length})
                </Space>
              }
            >
            <List
              dataSource={currentCompound.company_products}
              renderItem={(product: any, index: number) => (
                <List.Item key={index} style={{ padding: '16px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <div style={{ width: '100%' }}>
                    {/* 产品标题行 */}
                    <div style={{ marginBottom: '12px' }}>
                      <Space>
                        <Tag color="blue">{product.company_short_name}</Tag>
                        <span style={{ fontWeight: 'bold', fontSize: '16px' }}>{product.full_model_name}</span>
                        {product.status === '停产' && (
                          <Tag color="red">停产</Tag>
                        )}
                      </Space>
                    </div>

                    {/* 企业名称单独一行 */}
                    <div style={{ marginBottom: '12px', padding: '8px 12px', background: '#fafafa', borderRadius: '4px' }}>
                      <span style={{ fontWeight: 'bold', color: '#1890ff' }}>企业:</span> {product.company_name}
                    </div>

                    {/* 产品参数 */}
                    <Row gutter={[16, 8]} style={{ marginBottom: '12px' }}>
                      {product.assay && (
                        <Col span={8}>
                          <span style={{ fontWeight: 'bold' }}>含量:</span> {product.assay}
                        </Col>
                      )}
                      {product.physical_form && (
                        <Col span={8}>
                          <span style={{ fontWeight: 'bold' }}>物理形态:</span> {product.physical_form}
                        </Col>
                      )}
                      {product.status && (
                        <Col span={8}>
                          <span style={{ fontWeight: 'bold' }}>状态:</span> {product.status}
                        </Col>
                      )}
                    </Row>

                    {/* 应用描述 */}
                    {product.applications && (
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>应用:</div>
                        <div style={{ padding: '8px 12px', background: '#f5f5f5', borderRadius: '4px', lineHeight: '1.6' }}>
                          {product.applications}
                        </div>
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
            </Card>
          )}

        </div>

      </div>
    </div>
  );
};

export default ChemicalDetailSimple;
