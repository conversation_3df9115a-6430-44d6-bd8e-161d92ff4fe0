import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import Header from './components/Layout/Header';
import Footer from './components/Layout/Footer';
import HomePage from './pages/HomePage';
import SearchPage from './pages/SearchPage';
import ChemicalDetailSimple from './pages/ChemicalDetailSimple';
import StatisticsPage from './pages/StatisticsPage';
import CompanyListPage from './pages/CompanyListPage';
import CompanyDetailPage from './pages/CompanyDetailPage';

const { Content } = Layout;

const App: React.FC = () => {
  return (
    <Layout className="min-h-screen">
      <Header />
      <Content className="flex-1">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/search" element={<SearchPage />} />
          <Route path="/chemical/:casNumber" element={<ChemicalDetailSimple />} />
          <Route path="/statistics" element={<StatisticsPage />} />
          <Route path="/companies" element={<CompanyListPage />} />
          <Route path="/companies/:id" element={<CompanyDetailPage />} />
        </Routes>
      </Content>
      <Footer />
    </Layout>
  );
};

export default App;
