import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

export const chemicalAPI = {
  // 获取化学品列表
  getChemicals: (params: { page?: number; search?: string; page_size?: number } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.page_size) queryParams.append('page_size', params.page_size.toString());
    
    return apiClient.get(`/chemicals/?${queryParams.toString()}`);
  },

  // 获取化学品详情
  getChemicalDetail: (casNumber: string) => {
    return apiClient.get(`/chemicals/${casNumber}/`);
  },

  // 搜索化学品
  searchChemicals: (searchParams: {
    query: string;
    search_type: string;
  }) => {
    return apiClient.post('/chemicals/search/', searchParams);
  },

  // 获取统计信息
  getStatistics: () => {
    return apiClient.get('/chemicals/statistics/');
  },

  // 创建化学品
  createChemical: (data: any) => {
    return apiClient.post('/chemicals/', data);
  },

  // 更新化学品
  updateChemical: (casNumber: string, data: any) => {
    return apiClient.put(`/chemicals/${casNumber}/`, data);
  },

  // 删除化学品
  deleteChemical: (casNumber: string) => {
    return apiClient.delete(`/chemicals/${casNumber}/`);
  },
};

export const companyAPI = {
  // 获取企业列表
  getCompanies: (params: { page?: number; country?: string; business_type?: string } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.country) queryParams.append('country', params.country);
    if (params.business_type) queryParams.append('business_type', params.business_type);

    return apiClient.get(`/companies/?${queryParams.toString()}`);
  },

  // 获取企业详情
  getCompanyDetail: (companyId: number) => {
    return apiClient.get(`/companies/${companyId}/`);
  },

  // 根据CAS号查找供应商
  getSuppliersByCas: (casNumber: string) => {
    return apiClient.get(`/companies/suppliers/${casNumber}/`);
  },

  // 搜索企业
  searchCompanies: (params: { q?: string; country?: string; business_type?: string }) => {
    const queryParams = new URLSearchParams();
    if (params.q) queryParams.append('q', params.q);
    if (params.country) queryParams.append('country', params.country);
    if (params.business_type) queryParams.append('business_type', params.business_type);

    return apiClient.get(`/companies/search/?${queryParams.toString()}`);
  },

  // 获取企业统计信息
  getCompanyStatistics: () => {
    return apiClient.get('/companies/statistics/');
  },
};

export default apiClient;
