import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { companyAPI } from '../../services/api';

export interface Company {
  id: number;
  name: string;
  name_en: string;
  country: string;
  province?: string;
  city?: string;
  website?: string;
  business_type: string;
  company_scale: string;
  is_verified: boolean;
}

export interface CompanyProduct {
  company: Company;
  product_name_cn: string;
  product_name_en: string;
  product_code: string;
  purity: string;
  appearance: string;
  package_size: string;
  package_type: string;
  price_range: string;
  currency: string;
  min_order_quantity: string;
  lead_time: string;
  is_available: boolean;
  stock_status: string;
  product_description: string;
  applications: string;
  source_url: string;
  latest_price?: any;
}

export interface CompanyState {
  companies: Company[];
  currentCompany: Company | null;
  suppliers: CompanyProduct[];
  loading: boolean;
  error: string | null;
  companyStatistics: any;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
}

const initialState: CompanyState = {
  companies: [],
  currentCompany: null,
  suppliers: [],
  loading: false,
  error: null,
  companyStatistics: null,
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
};

// 异步操作
export const fetchCompanies = createAsyncThunk(
  'companies/fetchCompanies',
  async (params: { page?: number; country?: string; business_type?: string } = {}) => {
    const response = await companyAPI.getCompanies(params);
    return response.data;
  }
);

export const fetchCompanyDetail = createAsyncThunk(
  'companies/fetchCompanyDetail',
  async (companyId: number) => {
    const response = await companyAPI.getCompanyDetail(companyId);
    return response.data;
  }
);

export const fetchSuppliersByCas = createAsyncThunk(
  'companies/fetchSuppliersByCas',
  async (casNumber: string) => {
    const response = await companyAPI.getSuppliersByCas(casNumber);
    return response.data;
  }
);

export const searchCompanies = createAsyncThunk(
  'companies/searchCompanies',
  async (params: { q?: string; country?: string; business_type?: string }) => {
    const response = await companyAPI.searchCompanies(params);
    return response.data;
  }
);

export const fetchCompanyStatistics = createAsyncThunk(
  'companies/fetchCompanyStatistics',
  async () => {
    const response = await companyAPI.getCompanyStatistics();
    return response.data;
  }
);

const companySlice = createSlice({
  name: 'companies',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuppliers: (state) => {
      state.suppliers = [];
    },
    setCurrentCompany: (state, action: PayloadAction<Company | null>) => {
      state.currentCompany = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchCompanies
      .addCase(fetchCompanies.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCompanies.fulfilled, (state, action) => {
        state.loading = false;
        state.companies = action.payload.results;
        state.pagination.total = action.payload.count;
      })
      .addCase(fetchCompanies.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取企业列表失败';
      })
      // fetchCompanyDetail
      .addCase(fetchCompanyDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCompanyDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCompany = action.payload;
      })
      .addCase(fetchCompanyDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取企业详情失败';
      })
      // fetchSuppliersByCas
      .addCase(fetchSuppliersByCas.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSuppliersByCas.fulfilled, (state, action) => {
        state.loading = false;
        state.suppliers = action.payload.suppliers;
      })
      .addCase(fetchSuppliersByCas.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取供应商失败';
      })
      // searchCompanies
      .addCase(searchCompanies.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchCompanies.fulfilled, (state, action) => {
        state.loading = false;
        state.companies = action.payload;
      })
      .addCase(searchCompanies.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '搜索企业失败';
      })
      // fetchCompanyStatistics
      .addCase(fetchCompanyStatistics.fulfilled, (state, action) => {
        state.companyStatistics = action.payload;
      });
  },
});

export const { clearError, clearSuppliers, setCurrentCompany } = companySlice.actions;
export default companySlice.reducer;
