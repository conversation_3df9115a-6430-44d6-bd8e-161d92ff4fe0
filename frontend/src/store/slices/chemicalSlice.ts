import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { chemicalAPI } from '../../services/api';

export interface ChemicalCompound {
  cas_number: string;
  chinese_name: string;
  english_name: string;
  molecular_formula: string;
  molecular_weight?: string;
  category?: string;
  data_completeness?: number;
  einecs_number?: string;
  mdl_number?: string;

  // 物理化学性质
  appearance?: string;
  appearance_state?: string;
  solubility?: string;
  melting_point?: string;
  boiling_point?: string;
  density?: string;
  flash_point?: string;
  storage_conditions?: string;
  stability?: string;

  // 安全数据
  hazard_symbols_ghs?: string;
  warning_words?: string;
  hazard_description?: string;
  prevention_statement?: string;
  hazard_symbols?: string;
  risk_codes?: string;
  safety_statements?: string;
  transport_number?: string;

  // 数据库引用
  merck?: string;
  brn?: string;
  cas_database?: string;
  nist_info?: string;
  epa_info?: string;

  // 别名
  chinese_aliases?: string[];
  english_aliases?: string[];

  // 元数据
  created_at?: string;
  updated_at?: string;
  properties?: any[];
  aliases?: any[];
  company_products?: any[];
}

export interface ChemicalState {
  compounds: ChemicalCompound[];
  currentCompound: ChemicalCompound | null;
  loading: boolean;
  error: string | null;
  searchResults: ChemicalCompound[];
  statistics: any;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
}

const initialState: ChemicalState = {
  compounds: [],
  currentCompound: null,
  loading: false,
  error: null,
  searchResults: [],
  statistics: null,
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
};

// 异步操作
export const fetchChemicals = createAsyncThunk(
  'chemicals/fetchChemicals',
  async (params: { page?: number; search?: string; page_size?: number } = {}) => {
    console.log('fetchChemicals: 开始请求', params);
    const response = await chemicalAPI.getChemicals(params);
    console.log('fetchChemicals: 响应数据', response.data);
    return response.data;
  }
);

export const fetchChemicalDetail = createAsyncThunk(
  'chemicals/fetchChemicalDetail',
  async (casNumber: string) => {
    const response = await chemicalAPI.getChemicalDetail(casNumber);
    return response.data;
  }
);

export const searchChemicals = createAsyncThunk(
  'chemicals/searchChemicals',
  async (searchParams: { query: string; search_type: string; }) => {
    const response = await chemicalAPI.searchChemicals(searchParams);
    return response.data;
  }
);

export const fetchStatistics = createAsyncThunk(
  'chemicals/fetchStatistics',
  async () => {
    const response = await chemicalAPI.getStatistics();
    return response.data;
  }
);

const chemicalSlice = createSlice({
  name: 'chemicals',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    setCurrentCompound: (state, action: PayloadAction<ChemicalCompound | null>) => {
      state.currentCompound = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchChemicals
      .addCase(fetchChemicals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChemicals.fulfilled, (state, action) => {
        console.log('fetchChemicals.fulfilled: 更新状态', action.payload);
        state.loading = false;
        state.compounds = action.payload.results || action.payload; // 兼容不同响应格式
        state.pagination.total = action.payload.count || action.payload.length;
        console.log('fetchChemicals.fulfilled: 新状态', { compounds: state.compounds.length, total: state.pagination.total });
      })
      .addCase(fetchChemicals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取化学品列表失败';
      })
      // fetchChemicalDetail
      .addCase(fetchChemicalDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChemicalDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCompound = action.payload;
      })
      .addCase(fetchChemicalDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取化学品详情失败';
      })
      // searchChemicals
      .addCase(searchChemicals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchChemicals.fulfilled, (state, action) => {
        state.loading = false;
        state.searchResults = action.payload.results;
      })
      .addCase(searchChemicals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '搜索失败';
      })
      // fetchStatistics
      .addCase(fetchStatistics.fulfilled, (state, action) => {
        state.statistics = action.payload;
      });
  },
});

export const { clearError, clearSearchResults, setCurrentCompound } = chemicalSlice.actions;
export default chemicalSlice.reducer;
