<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>API连接测试</h1>
    <button onclick="testAPI()">测试API连接</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">正在测试API连接...</div>';
            
            try {
                // 测试统计API
                const statsResponse = await fetch('http://localhost:8001/api/chemicals/statistics/');
                const statsData = await statsResponse.json();
                
                // 测试化学品列表API
                const chemicalsResponse = await fetch('http://localhost:8001/api/chemicals/?page=1&page_size=3');
                const chemicalsData = await chemicalsResponse.json();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ API连接成功！</h3>
                        <p><strong>化学品总数:</strong> ${statsData.total_compounds}</p>
                        <p><strong>双语数据:</strong> ${statsData.compounds_with_both_languages}</p>
                        <p><strong>数据完整率:</strong> ${statsData.data_completeness_rate}%</p>
                        <h4>前3个化学品:</h4>
                        <ul>
                            ${chemicalsData.results.map(item => 
                                `<li>${item.cas_number} - ${item.compound_name_cn || item.compound_name_en}</li>`
                            ).join('')}
                        </ul>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ API连接失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <p><strong>可能原因:</strong></p>
                        <ul>
                            <li>后端服务器未启动 (http://localhost:8001)</li>
                            <li>CORS配置问题</li>
                            <li>网络连接问题</li>
                        </ul>
                    </div>
                `;
                console.error('API测试失败:', error);
            }
        }
        
        // 页面加载时自动测试
        window.onload = testAPI;
    </script>
</body>
</html>
