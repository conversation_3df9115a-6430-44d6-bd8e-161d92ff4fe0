{"name": "chinaorganicperoxide-frontend", "version": "1.0.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "antd": "^5.12.8", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "tailwindcss": "^3.3.6", "typescript": "^4.9.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "SKIP_PREFLIGHT_CHECK=true TSC_COMPILE_ON_ERROR=true react-scripts start", "build": "SKIP_PREFLIGHT_CHECK=true TSC_COMPILE_ON_ERROR=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}