
# 多阶段构建
# 第一阶段：构建React应用
FROM node:18-alpine as build

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制环境变量文件（本地测试用）
COPY .env.docker .env.production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 第二阶段：使用Nginx提供静态文件
FROM nginx:alpine

# 复制构建文件到Nginx目录
COPY --from=build /app/build /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]

