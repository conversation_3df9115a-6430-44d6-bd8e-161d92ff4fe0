# 生产环境前端Dockerfile - 多阶段构建
# 第一阶段：构建React应用
FROM node:18-alpine as build

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制环境变量文件
COPY .env.production .env.production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 第二阶段：生产环境Nginx
FROM nginx:alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 删除默认nginx配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制自定义nginx配置
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

# 复制构建文件到nginx目录
COPY --from=build /app/build /usr/share/nginx/html

# 创建SSL证书目录
RUN mkdir -p /etc/nginx/ssl

# 设置正确的权限
RUN chown -R nginx:nginx /usr/share/nginx/html \
    && chmod -R 755 /usr/share/nginx/html

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
