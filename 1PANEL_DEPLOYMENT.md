# 🚀 1Panel部署指南

## 📋 部署文件清单

### 生产环境文件
- `docker-compose.prod.yml` - 生产环境Docker Compose配置
- `backend/Dockerfile.prod` - 后端生产环境镜像
- `frontend/Dockerfile.prod` - 前端生产环境镜像
- `frontend/nginx.prod.conf` - 生产环境Nginx配置
- `deploy-1panel.sh` - 自动部署脚本

## 🔧 1Panel安全部署步骤

### ⚠️ 安全保证
**此部署方案确保不会影响任何现有生产环境服务：**
- 使用独立项目名称：`chinaorganicperoxide-prod`
- 使用不冲突端口：8001, 8080, 8443, 5433
- 使用独立网络和数据卷
- 包含部署前安全检查

### 1. 上传项目文件

将以下文件上传到1Panel服务器：

```
ChinaOrganicPeroxide/
├── docker-compose.prod.yml      # 生产环境配置
├── deploy-1panel.sh            # 安全部署脚本
├── pre-deploy-check.sh         # 部署前检查脚本
├── backend/
│   ├── Dockerfile.prod
│   ├── requirements.txt
│   ├── manage.py
│   ├── config/
│   ├── chemicals/
│   ├── companies/
│   └── ... (所有后端文件)
├── frontend/
│   ├── Dockerfile.prod
│   ├── nginx.prod.conf
│   ├── package.json
│   ├── src/
│   ├── public/
│   └── ... (所有前端文件)
└── ssl/ (SSL证书目录)
    ├── fullchain.pem
    └── privkey.pem
```

### 2. 部署前安全检查

**强烈建议先运行安全检查：**
```bash
# 进入项目目录
cd /opt/1panel/apps/chinaorganicperoxide

# 给脚本执行权限
chmod +x pre-deploy-check.sh
chmod +x deploy-1panel.sh

# 运行部署前检查
./pre-deploy-check.sh
```

### 3. 执行安全部署

确认检查无误后，运行部署：
```bash
# 安全部署（不影响现有服务）
./deploy-1panel.sh
```

### 4. 部署后验证

部署完成后，新服务将运行在：
- **前端**: http://localhost:8080
- **后端**: http://localhost:8001/api/
- **数据库**: localhost:5433

### 3. SSL证书配置

#### 获取SSL证书
```bash
# 使用Let's Encrypt
certbot certonly --standalone \
  -d www.chinaorganicperoxide.com \
  -d chinaorganicperoxide.com

# 复制证书到项目目录
mkdir -p ssl
cp /etc/letsencrypt/live/www.chinaorganicperoxide.com/fullchain.pem ssl/
cp /etc/letsencrypt/live/www.chinaorganicperoxide.com/privkey.pem ssl/
```

#### 或使用1Panel SSL管理
1. 在1Panel中进入"网站" → "SSL证书"
2. 申请或上传SSL证书
3. 将证书文件复制到项目的 `ssl/` 目录

### 4. 域名配置

在DNS管理中添加A记录：
```
www.chinaorganicperoxide.com  →  服务器IP
chinaorganicperoxide.com      →  服务器IP
```

### 5. 防火墙配置

确保以下端口开放：
```bash
# HTTP/HTTPS
ufw allow 80
ufw allow 443

# SSH (如果需要)
ufw allow 22

# 启用防火墙
ufw enable
```

## 🔍 验证部署

### 1. 检查容器状态
```bash
docker-compose -f docker-compose.prod.yml ps
```

### 2. 检查服务健康
```bash
# 后端API
curl https://api.chinaorganicperoxide.com/api/chemicals/

# 前端
curl https://www.chinaorganicperoxide.com
```

### 3. 查看日志
```bash
# 所有服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 特定服务日志
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f db
```

## 🔧 管理命令

### 启动/停止服务
```bash
# 启动
docker-compose -f docker-compose.prod.yml up -d

# 停止
docker-compose -f docker-compose.prod.yml down

# 重启
docker-compose -f docker-compose.prod.yml restart
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
./deploy-1panel.sh
```

### 数据库管理
```bash
# 进入数据库
docker-compose -f docker-compose.prod.yml exec db psql -U chinaorganicperoxide -d chinaorganicperoxide

# 备份数据库
docker-compose -f docker-compose.prod.yml exec db pg_dump -U chinaorganicperoxide chinaorganicperoxide > backup.sql

# 恢复数据库
docker-compose -f docker-compose.prod.yml exec -T db psql -U chinaorganicperoxide -d chinaorganicperoxide < backup.sql
```

## 🚨 故障排除

### 1. 容器启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.prod.yml logs [service_name]

# 检查镜像
docker images | grep chinaorganicperoxide
```

### 2. SSL证书问题
```bash
# 检查证书文件
ls -la ssl/
openssl x509 -in ssl/fullchain.pem -text -noout
```

### 3. 数据库连接问题
```bash
# 检查数据库容器
docker-compose -f docker-compose.prod.yml exec db pg_isready -U chinaorganicperoxide

# 测试连接
docker-compose -f docker-compose.prod.yml exec backend python manage.py check --database default
```

### 4. 网络问题
```bash
# 检查网络
docker network ls
docker-compose -f docker-compose.prod.yml exec backend ping db
```

## 📊 监控和维护

### 1. 资源监控
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h
docker system df
```

### 2. 日志轮转
```bash
# 清理旧日志
docker system prune -f

# 设置日志大小限制（在docker-compose.yml中）
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 3. 定期备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose -f docker-compose.prod.yml exec db pg_dump -U chinaorganicperoxide chinaorganicperoxide > backup_$DATE.sql
```

## 🎯 性能优化

### 1. 数据库优化
- 定期执行 `VACUUM` 和 `ANALYZE`
- 监控慢查询
- 适当调整PostgreSQL配置

### 2. 静态文件优化
- 使用CDN加速静态文件
- 启用Gzip压缩
- 设置合适的缓存策略

### 3. 容器优化
- 限制容器内存使用
- 使用多阶段构建减小镜像大小
- 定期清理无用镜像和容器

## 📞 技术支持

如遇问题，请检查：
1. 1Panel系统日志
2. Docker容器日志
3. 应用程序日志
4. 系统资源使用情况

联系方式：请通过GitHub Issues报告问题。
