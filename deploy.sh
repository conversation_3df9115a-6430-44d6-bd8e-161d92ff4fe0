#!/bin/bash

# 中国有机过氧化物数据库部署脚本

echo "🚀 开始部署中国有机过氧化物数据库..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理旧镜像（可选）
echo "🧹 清理旧镜像..."
docker system prune -f

# 构建新镜像
echo "🔨 构建新镜像..."
docker-compose build --no-cache

# 启动服务
echo "▶️ 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
docker-compose exec backend python manage.py migrate

# 收集静态文件
echo "📁 收集静态文件..."
docker-compose exec backend python manage.py collectstatic --noinput

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo "✅ 部署完成！"
echo "前端地址: https://www.chinaorganicperoxide.com"
echo "后端API: https://api.chinaorganicperoxide.com"
echo "API文档: https://api.chinaorganicperoxide.com/api/schema/swagger-ui/"
