#!/bin/bash

# 创建1Panel部署包脚本

echo "📦 创建1Panel部署包..."

# 创建部署目录
mkdir -p deployment-package

# 复制必要的文件
echo "📁 复制部署文件..."

# 根目录文件
cp docker-compose.prod.yml deployment-package/
cp deploy-1panel.sh deployment-package/
cp 1PANEL_DEPLOYMENT.md deployment-package/
cp README.md deployment-package/

# 后端文件
mkdir -p deployment-package/backend
cp -r backend/* deployment-package/backend/
# 排除不需要的文件
rm -rf deployment-package/backend/venv
rm -rf deployment-package/backend/__pycache__
rm -rf deployment-package/backend/*.log
rm -f deployment-package/backend/db.sqlite3

# 前端文件
mkdir -p deployment-package/frontend
cp -r frontend/* deployment-package/frontend/
# 排除不需要的文件
rm -rf deployment-package/frontend/node_modules
rm -rf deployment-package/frontend/build

# 创建SSL目录
mkdir -p deployment-package/ssl
echo "# 请将SSL证书文件放在此目录：" > deployment-package/ssl/README.txt
echo "# - fullchain.pem" >> deployment-package/ssl/README.txt
echo "# - privkey.pem" >> deployment-package/ssl/README.txt

# 创建压缩包
echo "🗜️ 创建压缩包..."
tar -czf chinaorganicperoxide-1panel-deployment.tar.gz deployment-package/

# 清理临时目录
rm -rf deployment-package

echo "✅ 部署包创建完成: chinaorganicperoxide-1panel-deployment.tar.gz"
echo ""
echo "📋 部署包内容："
echo "- docker-compose.prod.yml (生产环境配置)"
echo "- deploy-1panel.sh (自动部署脚本)"
echo "- 1PANEL_DEPLOYMENT.md (部署说明文档)"
echo "- backend/ (后端代码和配置)"
echo "- frontend/ (前端代码和配置)"
echo "- ssl/ (SSL证书目录)"
echo ""
echo "🚀 使用方法："
echo "1. 上传 chinaorganicperoxide-1panel-deployment.tar.gz 到服务器"
echo "2. 解压: tar -xzf chinaorganicperoxide-1panel-deployment.tar.gz"
echo "3. 进入目录: cd deployment-package"
echo "4. 运行部署: ./deploy-1panel.sh"
