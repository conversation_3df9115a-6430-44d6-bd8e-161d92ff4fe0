version: '3.8'

services:
  db:
    image: postgres:15
    container_name: chinaorganicperoxide-prod-db
    environment:
      POSTGRES_DB: chinaorganicperoxide
      POSTGRES_USER: chinaorganicperoxide
      POSTGRES_PASSWORD: XimNFK5w4eQ7WCfs
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chinaorganicperoxide"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network-prod

  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: chinaorganicperoxide-prod-backend
    ports:
      - "8001:8000"
    environment:
      - DB_NAME=chinaorganicperoxide
      - DB_USER=chinaorganicperoxide
      - DB_PASSWORD=XimNFK5w4eQ7WCfs
      - DB_HOST=db
      - DB_PORT=5432
      - SECRET_KEY=your-production-secret-key-change-this-in-production
      - DEBUG=False
      - ALLOWED_HOSTS=api.chinaorganicperoxide.com,www.chinaorganicperoxide.com,localhost
      - CORS_ALLOWED_ORIGINS=https://www.chinaorganicperoxide.com,https://chinaorganicperoxide.com
    volumes:
      - static_volume_prod:/app/staticfiles
      - media_volume_prod:/app/media
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/chemicals/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network-prod

  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: chinaorganicperoxide-prod-frontend
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - static_volume_prod:/usr/share/nginx/html/static
      - ./ssl:/etc/nginx/ssl:ro
    restart: unless-stopped
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network-prod

volumes:
  postgres_data_prod:
    name: chinaorganicperoxide_prod_postgres_data
  static_volume_prod:
    name: chinaorganicperoxide_prod_static_volume
  media_volume_prod:
    name: chinaorganicperoxide_prod_media_volume

networks:
  app-network-prod:
    name: chinaorganicperoxide_prod_network
    driver: bridge
