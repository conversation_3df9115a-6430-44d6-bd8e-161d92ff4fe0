#!/bin/bash

# 1Panel安全部署脚本 - 中国有机过氧化物数据库
# 此脚本设计为不影响任何现有生产环境服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称（用于隔离）
PROJECT_NAME="chinaorganicperoxide-prod"

echo -e "${BLUE}🚀 1Panel安全部署脚本${NC}"
echo -e "${GREEN}✅ 此脚本不会影响任何现有容器或服务${NC}"
echo "================================================"

# 安全检查函数
check_safety() {
    echo -e "${YELLOW}🔒 执行安全检查...${NC}"

    # 检查是否有同名容器运行
    if docker ps --format "table {{.Names}}" | grep -q "chinaorganicperoxide-prod"; then
        echo -e "${YELLOW}⚠️ 发现同名生产容器正在运行${NC}"
        echo -e "${YELLOW}是否继续？这将重新部署生产环境 (y/N):${NC}"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo -e "${GREEN}✅ 部署已取消，现有服务未受影响${NC}"
            exit 0
        fi
    fi

    # 检查端口占用
    local ports=(8001 8080 8443 5433)
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            echo -e "${YELLOW}⚠️ 端口 $port 已被占用${NC}"
            echo -e "${YELLOW}请确认这不会影响现有服务${NC}"
        fi
    done
}

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 检查是否在项目根目录
if [ ! -f "docker-compose.prod.yml" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    echo -e "${RED}❌ 缺少 docker-compose.prod.yml 文件${NC}"
    exit 1
fi

# 执行安全检查
check_safety

echo -e "${YELLOW}📦 开始安全部署...${NC}"

# 显示将要使用的端口
echo -e "${BLUE}📋 部署信息:${NC}"
echo -e "  项目名称: ${PROJECT_NAME}"
echo -e "  数据库端口: 5433 (不冲突5432)"
echo -e "  后端端口: 8001 (不冲突8000)"
echo -e "  前端HTTP: 8080 (不冲突80)"
echo -e "  前端HTTPS: 8443 (不冲突443)"
echo ""

# 不执行任何可能影响现有环境的清理操作
echo -e "${GREEN}✅ 跳过镜像清理，保护现有环境${NC}"

# 构建镜像（使用项目名称隔离）
echo -e "${YELLOW}🔨 构建生产环境镜像...${NC}"
docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} build --no-cache

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 镜像构建失败${NC}"
    echo -e "${RED}❌ 现有服务未受影响${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 镜像构建成功${NC}"

# 启动服务（使用项目名称隔离）
echo -e "${YELLOW}▶️ 启动生产环境服务...${NC}"
echo -e "${BLUE}ℹ️ 使用独立项目名称: ${PROJECT_NAME}${NC}"
docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} up -d

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo -e "${RED}❌ 现有服务未受影响${NC}"
    exit 1
fi

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 运行数据库迁移
echo -e "${YELLOW}🗄️ 运行数据库迁移...${NC}"
docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} exec backend python manage.py migrate

# 收集静态文件
echo -e "${YELLOW}📁 收集静态文件...${NC}"
docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} exec backend python manage.py collectstatic --noinput

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} ps

# 健康检查
echo -e "${YELLOW}🏥 健康检查...${NC}"
sleep 10

# 检查后端
if curl -f http://localhost:8001/api/chemicals/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端服务正常 (端口8001)${NC}"
else
    echo -e "${YELLOW}⚠️ 后端服务可能还在启动中${NC}"
fi

# 检查前端
if curl -f http://localhost:8080/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 前端服务正常 (端口8080)${NC}"
else
    echo -e "${YELLOW}⚠️ 前端服务可能还在启动中${NC}"
fi

echo ""
echo -e "${GREEN}🎉 安全部署完成！${NC}"
echo "================================================"
echo -e "${GREEN}✅ 现有生产环境服务未受任何影响${NC}"
echo ""
echo -e "${BLUE}📋 新部署的服务信息:${NC}"
echo -e "🌐 前端地址: ${BLUE}http://localhost:8080${NC}"
echo -e "🔌 后端API: ${BLUE}http://localhost:8001/api/${NC}"
echo -e "📚 API文档: ${BLUE}http://localhost:8001/api/schema/swagger-ui/${NC}"
echo -e "🗄️ 数据库: ${BLUE}localhost:5433${NC}"
echo ""
echo -e "${YELLOW}🔧 生产环境配置 (需要配置反向代理):${NC}"
echo -e "🌐 目标前端: ${BLUE}https://www.chinaorganicperoxide.com${NC}"
echo -e "🔌 目标API: ${BLUE}https://api.chinaorganicperoxide.com${NC}"
echo ""
echo -e "${YELLOW}📝 后续步骤:${NC}"
echo "1. 配置Nginx反向代理 (8080->80, 8001->API)"
echo "2. 上传SSL证书到 ./ssl/ 目录"
echo "3. 配置域名DNS解析"
echo "4. 配置防火墙规则"
echo "5. 设置定期备份"
echo ""
echo -e "${YELLOW}🔧 管理命令 (使用项目名称):${NC}"
echo "查看日志: docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} logs -f"
echo "重启服务: docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} restart"
echo "停止服务: docker-compose -f docker-compose.prod.yml -p ${PROJECT_NAME} down"
echo ""
echo -e "${GREEN}🛡️ 安全提醒: 此部署使用独立项目名称和端口，不会影响现有服务${NC}"
