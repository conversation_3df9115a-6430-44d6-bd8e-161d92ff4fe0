version: '3.8'

services:
  backend:
    build: ./backend
    container_name: chinaorganicperoxide-backend
    ports:
      - "8000:8000"
    environment:
      - DB_NAME=chinaorganicperoxide
      - DB_USER=chinaorganicperoxide
      - DB_PASSWORD=XimNFK5w4eQ7WCfs
      - DB_HOST=host.docker.internal
      - DB_PORT=5432
      - SECRET_KEY=your-production-secret-key-change-this
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
      - CORS_ALLOWED_ORIGINS=http://localhost,http://localhost:3000
    volumes:
      - ./backend/staticfiles:/app/staticfiles
    # 连接外部PostgreSQL容器
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/chemicals/"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./frontend
    container_name: chinaorganicperoxide-frontend
    ports:
      - "80:80"
    restart: unless-stopped
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

# 使用外部PostgreSQL容器，无需volumes
