# 🚀 中国有机过氧化物数据库 - Docker部署指南

## 📋 部署架构

```
Internet
    ↓
[域名解析]
    ↓
┌─────────────────────────────────────┐
│              VPS Server             │
│                                     │
│  ┌─────────────┐  ┌─────────────┐   │
│  │  Frontend   │  │  Backend    │   │
│  │   :80       │  │   :8000     │   │
│  │ Nginx+React │  │Django+Gun<PERSON>│ │
│  └─────────────┘  └─────────────┘   │
│                         │           │
└─────────────────────────┼───────────┘
                          │
                    ┌─────────────┐
                    │ PostgreSQL  │
                    │ (External)  │
                    └─────────────┘
```

## 🎯 部署目标

- **前端域名**: https://www.chinaorganicperoxide.com
- **后端域名**: https://api.chinaorganicperoxide.com
- **数据库**: 外部PostgreSQL (1Panel-postgresql-KGjC:5432)

## 📦 容器配置

### Frontend容器
- **基础镜像**: nginx:alpine
- **端口**: 80
- **功能**: 提供React构建文件 + 处理路由

### Backend容器  
- **基础镜像**: python:3.9-slim
- **端口**: 8000
- **功能**: Django API + Gunicorn

## 🔧 环境配置

### 生产环境变量 (.env.production)
```env
# Django设置
SECRET_KEY=your-production-secret-key-change-this
DEBUG=False
ALLOWED_HOSTS=api.chinaorganicperoxide.com,www.chinaorganicperoxide.com,localhost

# 数据库设置
DB_NAME=chinaorganicperoxide
DB_USER=chinaorganicperoxide
DB_PASSWORD=XimNFK5w4eQ7WCfs
DB_HOST=1Panel-postgresql-KGjC
DB_PORT=5432

# CORS设置
CORS_ALLOWED_ORIGINS=https://www.chinaorganicperoxide.com,https://chinaorganicperoxide.com
```

### 前端环境变量 (frontend/.env.production)
```env
REACT_APP_API_URL=https://api.chinaorganicperoxide.com/api
GENERATE_SOURCEMAP=false
```

## 🚀 部署步骤

### 1. VPS环境准备

#### 安装Docker和Docker Compose
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo apt install docker-compose

# 添加用户到docker组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

#### 验证安装
```bash
docker --version
docker-compose --version
```

### 2. 代码部署

#### 克隆项目
```bash
git clone https://github.com/zhousulong/ChinaOrganicPeroxide.git
cd ChinaOrganicPeroxide
```

#### 检查配置文件
```bash
# 确认生产环境配置
cat .env.production
cat frontend/.env.production
```

### 3. 执行部署

#### 运行部署脚本
```bash
chmod +x deploy.sh
./deploy.sh
```

#### 手动部署（如果脚本失败）
```bash
# 停止现有容器
docker-compose down

# 构建镜像
docker-compose build --no-cache

# 启动服务
docker-compose up -d

# 运行迁移
docker-compose exec backend python manage.py migrate

# 收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput
```

### 4. 验证部署

#### 检查容器状态
```bash
docker-compose ps
docker-compose logs frontend
docker-compose logs backend
```

#### 测试API
```bash
curl http://localhost:8000/api/chemicals/
curl http://localhost/
```

## 🌐 域名配置

### DNS设置
在域名管理面板中添加A记录：
- `www.chinaorganicperoxide.com` → VPS IP
- `api.chinaorganicperoxide.com` → VPS IP

### 端口映射
- 前端容器:80 → www.chinaorganicperoxide.com
- 后端容器:8000 → api.chinaorganicperoxide.com

## 🔒 SSL证书配置

### 方案1: Cloudflare (推荐)
1. 将域名DNS托管到Cloudflare
2. 开启SSL/TLS加密
3. 设置代理状态为"已代理"

### 方案2: Let's Encrypt
```bash
# 安装certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d www.chinaorganicperoxide.com -d api.chinaorganicperoxide.com

# 配置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 维护命令

### 查看日志
```bash
docker-compose logs -f frontend
docker-compose logs -f backend
```

### 重启服务
```bash
docker-compose restart
```

### 更新代码
```bash
git pull origin main
docker-compose build --no-cache
docker-compose up -d
```

### 备份数据
```bash
# 数据库备份（在数据库服务器上执行）
pg_dump -h 1Panel-postgresql-KGjC -U chinaorganicperoxide chinaorganicperoxide > backup.sql
```

### 清理Docker
```bash
# 清理未使用的镜像
docker system prune -f

# 清理所有未使用的资源
docker system prune -a
```

## 🚨 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   docker-compose logs [service_name]
   ```

2. **数据库连接失败**
   - 检查网络连通性
   - 验证数据库凭据
   - 确认防火墙设置

3. **前端无法访问后端API**
   - 检查CORS配置
   - 验证域名解析
   - 确认端口映射

4. **静态文件404**
   ```bash
   docker-compose exec backend python manage.py collectstatic --noinput
   ```

### 监控命令
```bash
# 查看资源使用
docker stats

# 查看容器健康状态
docker-compose ps
```

## 📞 支持

如有问题，请检查：
1. Docker和Docker Compose版本
2. 网络连接和防火墙设置
3. 域名DNS解析
4. 数据库连接配置

---

**部署完成后访问地址：**
- 🌐 前端: https://www.chinaorganicperoxide.com
- 🔌 API: https://api.chinaorganicperoxide.com
- 📚 API文档: https://api.chinaorganicperoxide.com/api/schema/swagger-ui/
