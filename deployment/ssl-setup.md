# 🔒 SSL证书配置指南

## 方案选择

### 方案1: Cloudflare (推荐) ⭐⭐⭐⭐⭐

**优势:**
- 免费SSL证书
- 全球CDN加速
- DDoS防护
- 简单配置

**步骤:**
1. 注册Cloudflare账号
2. 添加域名 `chinaorganicperoxide.com`
3. 修改域名DNS服务器为Cloudflare提供的
4. 在Cloudflare中添加DNS记录:
   ```
   A    www    VPS_IP    已代理
   A    api    VPS_IP    已代理
   ```
5. SSL/TLS设置选择"完全"模式

### 方案2: Let's Encrypt

**优势:**
- 免费
- 自动续期
- 广泛支持

**步骤:**
```bash
# 1. 安装certbot
sudo apt update
sudo apt install certbot

# 2. 停止现有服务（临时）
docker-compose down

# 3. 获取证书
sudo certbot certonly --standalone \
  -d www.chinaorganicperoxide.com \
  -d api.chinaorganicperoxide.com

# 4. 设置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart
```

## Nginx SSL配置

如果使用Let's Encrypt，需要修改Nginx配置:

### 创建SSL Nginx配置
```nginx
server {
    listen 80;
    server_name www.chinaorganicperoxide.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.chinaorganicperoxide.com;
    
    ssl_certificate /etc/letsencrypt/live/www.chinaorganicperoxide.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.chinaorganicperoxide.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    root /usr/share/nginx/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 后端SSL配置
```nginx
server {
    listen 80;
    server_name api.chinaorganicperoxide.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.chinaorganicperoxide.com;
    
    ssl_certificate /etc/letsencrypt/live/api.chinaorganicperoxide.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.chinaorganicperoxide.com/privkey.pem;
    
    location / {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 防火墙配置

```bash
# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 22  # SSH

# 启用防火墙
sudo ufw enable
```

## 验证SSL

```bash
# 检查证书
openssl s_client -connect www.chinaorganicperoxide.com:443 -servername www.chinaorganicperoxide.com

# 在线检查
# https://www.ssllabs.com/ssltest/
```
