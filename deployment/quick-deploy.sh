#!/bin/bash

# 快速部署脚本 - 中国有机过氧化物数据库
# 使用方法: ./quick-deploy.sh

set -e

echo "🚀 中国有机过氧化物数据库 - 快速部署脚本"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ 请不要使用root用户运行此脚本${NC}"
    exit 1
fi

# 检查Docker
echo -e "${YELLOW}🔍 检查Docker环境...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，正在安装...${NC}"
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    echo -e "${GREEN}✅ Docker安装完成，请重新登录后再次运行此脚本${NC}"
    exit 0
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，正在安装...${NC}"
    sudo apt update
    sudo apt install -y docker-compose
fi

# 检查Docker服务
if ! sudo systemctl is-active --quiet docker; then
    echo -e "${YELLOW}🔄 启动Docker服务...${NC}"
    sudo systemctl start docker
    sudo systemctl enable docker
fi

echo -e "${GREEN}✅ Docker环境检查完成${NC}"

# 检查项目目录
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ 未找到docker-compose.yml文件，请确保在项目根目录运行${NC}"
    exit 1
fi

# 检查环境配置文件
if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ 未找到.env.production文件${NC}"
    exit 1
fi

if [ ! -f "frontend/.env.production" ]; then
    echo -e "${RED}❌ 未找到frontend/.env.production文件${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 配置文件检查完成${NC}"

# 停止现有容器
echo -e "${YELLOW}🛑 停止现有容器...${NC}"
docker-compose down 2>/dev/null || true

# 清理旧镜像
echo -e "${YELLOW}🧹 清理Docker缓存...${NC}"
docker system prune -f

# 构建镜像
echo -e "${YELLOW}🔨 构建Docker镜像...${NC}"
docker-compose build --no-cache

# 启动服务
echo -e "${YELLOW}▶️ 启动服务...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 检查容器状态
echo -e "${YELLOW}🔍 检查容器状态...${NC}"
if ! docker-compose ps | grep -q "Up"; then
    echo -e "${RED}❌ 容器启动失败，查看日志:${NC}"
    docker-compose logs
    exit 1
fi

# 运行数据库迁移
echo -e "${YELLOW}🗄️ 运行数据库迁移...${NC}"
docker-compose exec -T backend python manage.py migrate

# 收集静态文件
echo -e "${YELLOW}📁 收集静态文件...${NC}"
docker-compose exec -T backend python manage.py collectstatic --noinput

# 最终检查
echo -e "${YELLOW}🔍 最终健康检查...${NC}"
sleep 10

# 测试后端API
if curl -f http://localhost:8000/api/chemicals/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端API正常${NC}"
else
    echo -e "${RED}❌ 后端API异常${NC}"
fi

# 测试前端
if curl -f http://localhost/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 前端服务正常${NC}"
else
    echo -e "${RED}❌ 前端服务异常${NC}"
fi

# 显示容器状态
echo -e "${YELLOW}📊 容器状态:${NC}"
docker-compose ps

echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo "================================================"
echo -e "前端地址: ${GREEN}https://www.chinaorganicperoxide.com${NC}"
echo -e "后端API: ${GREEN}https://api.chinaorganicperoxide.com${NC}"
echo -e "API文档: ${GREEN}https://api.chinaorganicperoxide.com/api/schema/swagger-ui/${NC}"
echo ""
echo -e "${YELLOW}📝 后续步骤:${NC}"
echo "1. 配置域名DNS解析"
echo "2. 设置SSL证书"
echo "3. 配置防火墙规则"
echo ""
echo -e "${YELLOW}🔧 常用命令:${NC}"
echo "查看日志: docker-compose logs -f"
echo "重启服务: docker-compose restart"
echo "停止服务: docker-compose down"
