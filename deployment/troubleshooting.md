# 🚨 故障排除指南

## 常见问题及解决方案

### 1. 容器启动失败

#### 问题现象
```bash
docker-compose ps
# 显示容器状态为 "Exit 1" 或 "Restarting"
```

#### 解决步骤
```bash
# 查看详细日志
docker-compose logs frontend
docker-compose logs backend

# 检查配置文件
cat .env.production
cat frontend/.env.production

# 重新构建
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 2. 数据库连接失败

#### 问题现象
```
django.db.utils.OperationalError: could not connect to server
```

#### 解决步骤
```bash
# 1. 检查数据库连接
docker-compose exec backend python manage.py dbshell

# 2. 测试网络连通性
docker-compose exec backend ping 1Panel-postgresql-KGjC

# 3. 验证环境变量
docker-compose exec backend env | grep DB_

# 4. 检查数据库服务状态
# (在数据库服务器上执行)
sudo systemctl status postgresql
```

### 3. 前端无法访问后端API

#### 问题现象
- 前端页面加载但数据为空
- 浏览器控制台显示CORS错误
- API请求失败

#### 解决步骤
```bash
# 1. 检查CORS配置
docker-compose exec backend python manage.py shell
>>> from django.conf import settings
>>> print(settings.CORS_ALLOWED_ORIGINS)

# 2. 测试API直接访问
curl http://localhost:8000/api/chemicals/

# 3. 检查前端环境变量
docker-compose exec frontend cat /usr/share/nginx/html/static/js/main.*.js | grep -o 'REACT_APP_API_URL[^"]*'

# 4. 重新构建前端
docker-compose build frontend --no-cache
docker-compose up -d frontend
```

### 4. 静态文件404错误

#### 问题现象
- Django admin样式丢失
- API文档样式异常

#### 解决步骤
```bash
# 重新收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput

# 检查静态文件目录
docker-compose exec backend ls -la /app/staticfiles/

# 检查Django设置
docker-compose exec backend python manage.py shell
>>> from django.conf import settings
>>> print(settings.STATIC_ROOT)
>>> print(settings.STATIC_URL)
```

### 5. 内存不足

#### 问题现象
```
docker: Error response from daemon: OCI runtime create failed
```

#### 解决步骤
```bash
# 检查系统资源
free -h
df -h

# 清理Docker缓存
docker system prune -a

# 检查容器资源使用
docker stats

# 如果内存不足，考虑增加swap
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 6. 端口冲突

#### 问题现象
```
Error starting userland proxy: listen tcp 0.0.0.0:80: bind: address already in use
```

#### 解决步骤
```bash
# 查看端口占用
sudo netstat -tulpn | grep :80
sudo netstat -tulpn | grep :8000

# 停止占用端口的服务
sudo systemctl stop apache2  # 如果是Apache
sudo systemctl stop nginx    # 如果是Nginx

# 或者修改docker-compose.yml中的端口映射
```

### 7. 权限问题

#### 问题现象
```
Permission denied
```

#### 解决步骤
```bash
# 检查文件权限
ls -la docker-compose.yml
ls -la .env.production

# 修复权限
sudo chown -R $USER:$USER .
chmod +x deploy.sh

# 检查Docker权限
groups $USER
# 如果没有docker组，添加用户到docker组
sudo usermod -aG docker $USER
newgrp docker
```

## 监控和日志

### 实时监控
```bash
# 查看容器状态
watch docker-compose ps

# 查看资源使用
docker stats

# 实时日志
docker-compose logs -f
```

### 日志分析
```bash
# 查看最近的错误
docker-compose logs --tail=50 backend | grep -i error
docker-compose logs --tail=50 frontend | grep -i error

# 导出日志
docker-compose logs > deployment.log
```

### 健康检查
```bash
# 手动健康检查
curl -f http://localhost:8000/api/chemicals/ || echo "Backend failed"
curl -f http://localhost/ || echo "Frontend failed"

# 检查数据库连接
docker-compose exec backend python manage.py check --database default
```

## 性能优化

### 1. 数据库优化
```bash
# 检查数据库连接数
docker-compose exec backend python manage.py shell
>>> from django.db import connection
>>> print(connection.queries)
```

### 2. 内存优化
```bash
# 限制容器内存使用
# 在docker-compose.yml中添加:
# deploy:
#   resources:
#     limits:
#       memory: 512M
```

### 3. 磁盘空间
```bash
# 清理Docker
docker system prune -a

# 清理日志
sudo journalctl --vacuum-time=7d
```

## 紧急恢复

### 快速重启
```bash
# 完全重启
docker-compose down
docker-compose up -d

# 仅重启有问题的服务
docker-compose restart backend
docker-compose restart frontend
```

### 回滚部署
```bash
# 回到上一个版本
git log --oneline -5
git checkout <previous-commit>
docker-compose build --no-cache
docker-compose up -d
```

### 数据备份恢复
```bash
# 如果有数据库备份
# (在数据库服务器上执行)
psql -h 1Panel-postgresql-KGjC -U chinaorganicperoxide -d chinaorganicperoxide < backup.sql
```

## 联系支持

如果以上方法都无法解决问题：

1. 收集错误信息：
   ```bash
   docker-compose logs > error.log
   docker-compose ps > status.log
   ```

2. 检查系统信息：
   ```bash
   uname -a > system.log
   docker --version >> system.log
   docker-compose --version >> system.log
   ```

3. 提供详细的错误描述和重现步骤
