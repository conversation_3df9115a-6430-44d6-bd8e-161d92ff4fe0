# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
upload/

# Virtual environments - 重要！
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js - 重要！
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build
build/
.env.local
.env.development.local
.env.test.local
.env.production.local

# 环境变量文件
.env
*.env

# 数据文件 - 重要！
extracted_data.json
*.csv
*.xlsx
*.xls
data/
scraped_data/

# 配置文件中的敏感信息
*.key
*.pem

# 临时文件
*.tmp
*.temp
*.bak

# 缓存文件
.cache/
.pytest_cache/