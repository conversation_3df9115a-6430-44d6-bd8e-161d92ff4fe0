#!/bin/bash

# 中国有机过氧化物数据库 - 本地开发环境启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 启动中国有机过氧化物数据库开发环境...${NC}"
echo "================================================"

# 检查是否在项目根目录
if [ ! -f "LOCAL_DEVELOPMENT.md" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 1. 启动数据库
echo -e "${YELLOW}📊 检查并启动数据库...${NC}"
if docker ps | grep -q "chinaorganicperoxide-postgres-local"; then
    echo -e "${GREEN}✅ 数据库已在运行${NC}"
else
    echo -e "${YELLOW}🔄 启动数据库容器...${NC}"
    docker start chinaorganicperoxide-postgres-local
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库启动成功${NC}"
    else
        echo -e "${RED}❌ 数据库启动失败${NC}"
        exit 1
    fi
fi

# 2. 检查后端环境
echo -e "${YELLOW}🔧 检查后端环境...${NC}"
if [ ! -d "backend/venv" ]; then
    echo -e "${YELLOW}📦 创建Python虚拟环境...${NC}"
    cd backend
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
    echo -e "${GREEN}✅ 后端环境设置完成${NC}"
else
    echo -e "${GREEN}✅ 后端环境已存在${NC}"
fi

# 3. 检查前端环境
echo -e "${YELLOW}🎨 检查前端环境...${NC}"
if [ ! -d "frontend/node_modules" ]; then
    echo -e "${YELLOW}📦 安装前端依赖...${NC}"
    cd frontend
    npm install
    cd ..
    echo -e "${GREEN}✅ 前端环境设置完成${NC}"
else
    echo -e "${GREEN}✅ 前端环境已存在${NC}"
fi

echo ""
echo -e "${BLUE}🎯 启动服务...${NC}"
echo "================================================"

# 4. 启动后端（后台运行）
echo -e "${YELLOW}🔧 启动Django后端服务...${NC}"
cd backend
source venv/bin/activate
nohup python manage.py runserver 8000 > ../backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:8000/api/chemicals/ > /dev/null; then
    echo -e "${GREEN}✅ 后端启动成功 (PID: $BACKEND_PID)${NC}"
else
    echo -e "${RED}❌ 后端启动失败，检查 backend.log${NC}"
    exit 1
fi

# 5. 启动前端
echo -e "${YELLOW}🎨 启动React前端服务...${NC}"
cd frontend

echo ""
echo -e "${GREEN}🎉 启动完成！${NC}"
echo "================================================"
echo -e "📱 前端应用: ${BLUE}http://localhost:3000${NC}"
echo -e "🔌 后端API: ${BLUE}http://localhost:8000/api/${NC}"
echo -e "📚 API文档: ${BLUE}http://localhost:8000/api/schema/swagger-ui/${NC}"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "- 前端将在浏览器中自动打开"
echo "- 按 Ctrl+C 停止前端服务"
echo -e "- 后端日志: ${BLUE}backend.log${NC}"
echo -e "- 停止后端: ${BLUE}kill $BACKEND_PID${NC}"
echo ""

# 启动前端（前台运行）
npm start
