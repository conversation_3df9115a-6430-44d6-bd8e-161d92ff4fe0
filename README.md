# 中国有机过氧化物数据库

## 项目概述

这是一个专门收集、整理和展示中国有机过氧化物相关信息的数据库项目。项目包含化学品信息、企业信息和产品信息的完整数据管理系统。

## 技术栈

### 后端
- **Django 4.2.7** - Web框架
- **Django REST Framework** - API框架
- **SQLite** - 数据库
- **drf-spectacular** - API文档生成
- **django-cors-headers** - 跨域支持

### 前端
- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Ant Design** - UI组件库
- **Redux Toolkit** - 状态管理
- **React Router** - 路由管理

## 项目结构

```
ChinaOrganicPeroxide/
├── backend/                   # Django后端
│   ├── chemicals/            # 化学品应用
│   ├── companies/            # 企业应用
│   ├── config/               # Django配置
│   ├── db.sqlite3           # SQLite数据库
│   ├── manage.py            # Django管理脚本
│   └── requirements.txt     # Python依赖
├── frontend/                 # React前端
│   ├── src/                 # 源代码
│   ├── public/              # 静态文件
│   └── package.json         # 前端依赖
└── README.md                # 项目说明
```

## 快速启动

### 环境要求
- Python 3.9+
- Node.js 16+
- npm

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 启动Django开发服务器
python3 manage.py runserver 8000
```

### 2. 启动前端服务

```bash
# 进入前端目录
cd frontend

# 安装依赖（首次运行）
npm install

# 启动React开发服务器
npm start
```

### 3. 访问应用

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/schema/swagger-ui/

## 数据模型

### ChemicalCompound (化学品)
- 基本信息：CAS号、中英文名称、分子式、分子量
- 物理性质：熔点、沸点、密度、溶解度等
- 安全信息：危险性分类、储存条件、运输信息
- 应用信息：主要用途、工业应用

### ChemicalAlias (化学品别名)
- 别名信息：别名名称、语言类型
- 关联关系：关联化学品

### Company (企业)
- 基本信息：企业名称、地址、联系方式
- 企业属性：业务类型、企业规模
- 认证状态：是否验证

### CompanyProduct (企业产品)
- 产品信息：品牌、型号、含量、物理形态
- 生命周期状态：在产、停产、停售等
- 关联关系：关联企业和化学品

## 主要功能

- 🔍 化学品搜索和详情查看
- 🏢 企业信息管理
- 📦 产品信息展示
- 📊 数据统计分析
- 🔗 关联关系查询

## 开发说明

项目采用前后端分离架构：
- 后端提供RESTful API接口
- 前端通过API获取数据并展示
- 支持跨域请求
- 自动生成API文档

## 注意事项

- 确保Python和Node.js版本符合要求
- 首次运行前端需要执行 `npm install` 安装依赖
- 后端使用SQLite数据库，无需额外配置
- 开发环境下前端代理后端API请求到8000端口
