# 🚀 本地开发环境启动指南

## 📋 环境要求

- **Python**: 3.9+
- **Node.js**: 16+
- **Docker**: 用于运行PostgreSQL数据库
- **npm**: Node包管理器

## 🗂️ 项目架构

```
本地开发环境架构：
┌─────────────────────────────────────┐
│              本地环境                │
│  ┌─────────────┐  ┌─────────────┐   │
│  │  Frontend   │  │  Backend    │   │
│  │   :3000     │  │   :8000     │   │
│  │ React Dev   │  │Django Dev   │   │
│  └─────────────┘  └─────────────┘   │
│                         │           │
└─────────────────────────┼───────────┘
                          │
                    ┌─────────────┐
                    │ PostgreSQL  │
                    │ (Docker)    │
                    │   :5432     │
                    └─────────────┘
```

## 🔧 一次性环境设置

### 1. 后端环境设置

```bash
# 进入后端目录
cd backend

# 创建Python虚拟环境（仅首次）
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖（仅首次或依赖更新时）
pip install -r requirements.txt
```

### 2. 前端环境设置

```bash
# 进入前端目录
cd frontend

# 安装依赖（仅首次或依赖更新时）
npm install
```

### 3. 数据库设置

数据库使用Docker容器：`chinaorganicperoxide-postgres-local`
- 容器已包含完整数据（77个化学品，6个企业，536个产品）
- 无需额外配置

## 🚀 日常启动步骤

### 步骤1: 启动数据库

```bash
# 检查数据库容器状态
docker ps | grep postgres

# 如果容器未运行，启动它
docker start chinaorganicperoxide-postgres-local

# 验证数据库运行
docker ps | grep chinaorganicperoxide-postgres-local
```

### 步骤2: 启动后端服务

```bash
# 进入后端目录
cd backend

# 激活虚拟环境
source venv/bin/activate

# 启动Django开发服务器
python manage.py runserver 8000
```

**后端启动成功标志**：
```
Watching for file changes with StatReloader
Performing system checks...
System check identified no issues (0 silenced).
Django version 4.2.7, using settings 'config.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.
```

### 步骤3: 启动前端服务（新终端）

```bash
# 进入前端目录
cd frontend

# 启动React开发服务器
npm start
```

**前端启动成功标志**：
```
Compiled with warnings.
webpack compiled with 1 warning
Local:            http://localhost:3000
```

## 🌐 访问地址

启动完成后，可以访问以下地址：

- **🎨 前端应用**: http://localhost:3000
- **🔌 后端API**: http://localhost:8000/api/
- **📚 API文档**: http://localhost:8000/api/schema/swagger-ui/
- **⚙️ Django Admin**: http://localhost:8000/admin/

## 🔍 验证启动状态

### 检查后端API

```bash
# 测试API连接
curl http://localhost:8000/api/chemicals/

# 应该返回化学品列表JSON数据
```

### 检查前端

在浏览器中访问 http://localhost:3000，应该能看到：
- 首页正常加载
- 化学品列表显示数据
- 搜索功能正常

### 检查数据库

```bash
# 进入数据库容器
docker exec -it chinaorganicperoxide-postgres-local psql -U chinaorganicperoxide -d chinaorganicperoxide

# 检查数据
\dt  -- 查看表
SELECT COUNT(*) FROM chemicals_chemicalcompound;  -- 应该返回77
\q   -- 退出
```

## 🛑 停止服务

### 停止前端
在前端终端按 `Ctrl+C`

### 停止后端
在后端终端按 `Ctrl+C`

### 停止数据库（可选）
```bash
docker stop chinaorganicperoxide-postgres-local
```

## 🚨 常见问题

### 1. 数据库连接失败
```
django.db.utils.OperationalError: connection to server at "localhost"
```

**解决方案**：
```bash
# 检查数据库容器状态
docker ps | grep postgres

# 启动数据库容器
docker start chinaorganicperoxide-postgres-local
```

### 2. 端口被占用
```
Error: listen EADDRINUSE: address already in use :::3000
```

**解决方案**：
```bash
# 查找占用端口的进程
lsof -i :3000
lsof -i :8000

# 杀死进程或使用其他端口
kill -9 <PID>
```

### 3. 虚拟环境问题
```
ModuleNotFoundError: No module named 'django'
```

**解决方案**：
```bash
# 确保激活了虚拟环境
source backend/venv/bin/activate

# 重新安装依赖
pip install -r requirements.txt
```

### 4. 前端编译警告

前端可能显示TypeScript警告，但不影响运行：
```
TS2339: Property 'xxx' does not exist on type 'WritableDraft<ChemicalCompound>'
```

这些警告不影响功能，可以忽略。

## 📝 开发提示

### 热重载
- **后端**: Django自动检测文件变化并重载
- **前端**: React自动检测文件变化并热更新

### 调试
- **后端**: 可以在代码中添加 `print()` 或使用Django调试工具
- **前端**: 使用浏览器开发者工具

### 数据库管理
- 使用Django Admin: http://localhost:8000/admin/
- 使用数据库客户端连接: localhost:5432

## 🔄 快速启动脚本

可以创建启动脚本简化操作：

```bash
#!/bin/bash
# start_dev.sh

echo "🚀 启动中国有机过氧化物数据库开发环境..."

# 启动数据库
echo "📊 启动数据库..."
docker start chinaorganicperoxide-postgres-local

# 启动后端
echo "🔧 启动后端..."
cd backend
source venv/bin/activate
python manage.py runserver 8000 &

# 启动前端
echo "🎨 启动前端..."
cd ../frontend
npm start

echo "✅ 启动完成！"
echo "前端: http://localhost:3000"
echo "后端: http://localhost:8000"
```

使用方法：
```bash
chmod +x start_dev.sh
./start_dev.sh
```
