from rest_framework import generics, status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q, Count
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import Company, CompanyProduct
from .serializers import (
    CompanySerializer,
    CompanyDetailSerializer,
    CompanyProductSerializer,
    CompanyProductListSerializer
)


class CompanyPagination(PageNumberPagination):
    """企业分页器"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class CompanyListView(generics.ListAPIView):
    """企业列表视图"""
    queryset = Company.objects.filter(is_active=True)
    serializer_class = CompanySerializer
    pagination_class = CompanyPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['business_type', 'company_scale', 'is_verified']
    
    @extend_schema(
        summary="获取企业列表",
        description="获取有机过氧化物供应商企业列表",
        parameters=[
            OpenApiParameter(
                name='province',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='按省份过滤'
            ),
            OpenApiParameter(
                name='business_type',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='按企业类型过滤'
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class CompanyDetailView(generics.RetrieveAPIView):
    """企业详情视图"""
    queryset = Company.objects.filter(is_active=True).prefetch_related('products__chemical')
    serializer_class = CompanyDetailSerializer

    def get_object(self):
        """支持通过ID或英文短名称查找公司"""
        lookup_value = self.kwargs.get('pk') or self.kwargs.get('english_short_name')

        # 尝试通过ID查找
        if isinstance(lookup_value, int) or (isinstance(lookup_value, str) and lookup_value.isdigit()):
            try:
                return self.queryset.get(id=int(lookup_value))
            except Company.DoesNotExist:
                pass

        # 尝试通过英文短名称查找
        try:
            return self.queryset.get(english_short_name=lookup_value)
        except Company.DoesNotExist:
            pass

        # 如果都找不到，抛出404
        from django.http import Http404
        raise Http404("Company not found")

    @extend_schema(
        summary="获取企业详情",
        description="获取企业的详细信息和产品列表"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class CompanyProductListView(generics.ListAPIView):
    """企业产品列表视图"""
    serializer_class = CompanyProductListSerializer
    pagination_class = CompanyPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['lifecycle_status']
    
    def get_queryset(self):
        # 支持通过ID或英文短名称查找
        pk = self.kwargs.get('pk')
        english_short_name = self.kwargs.get('english_short_name')

        if pk:
            return CompanyProduct.objects.filter(
                company__id=pk
            ).select_related('company', 'chemical')
        elif english_short_name:
            return CompanyProduct.objects.filter(
                company__english_short_name=english_short_name
            ).select_related('company', 'chemical')
        else:
            return CompanyProduct.objects.none()
    
    @extend_schema(
        summary="获取企业产品列表",
        description="获取指定企业的产品列表"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@extend_schema(
    summary="根据CAS号查找供应商",
    description="根据CAS号查找所有供应该化学品的企业"
)
@api_view(['GET'])
def get_suppliers_by_cas(request, cas_number):
    """根据CAS号查找供应商"""
    try:
        products = CompanyProduct.objects.filter(
            chemical__cas_number=cas_number
        ).select_related('company', 'chemical')
        
        if not products.exists():
            return Response({
                'cas_number': cas_number,
                'suppliers': [],
                'message': '未找到该化学品的供应商'
            })
        
        serializer = CompanyProductSerializer(products, many=True)
        
        return Response({
            'cas_number': cas_number,
            'chemical_name_cn': products.first().chemical.compound_name_cn,
            'chemical_name_en': products.first().chemical.compound_name_en,
            'suppliers_count': products.count(),
            'suppliers': serializer.data
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="获取企业统计信息",
    description="获取企业和产品的统计信息"
)
@api_view(['GET'])
def get_company_statistics(request):
    """获取企业统计信息"""
    # 企业统计
    total_companies = Company.objects.filter(is_active=True).count()
    verified_companies = Company.objects.filter(is_active=True, is_verified=True).count()
    
    # 按类型统计
    type_stats = Company.objects.filter(is_active=True).values('business_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # 按省份统计
    province_stats = Company.objects.filter(is_active=True).exclude(province='').values('province').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # 按规模统计
    scale_stats = Company.objects.filter(is_active=True).values('company_scale').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # 产品统计
    total_products = CompanyProduct.objects.count()
    active_products = CompanyProduct.objects.filter(lifecycle_status='active').count()
    
    # 企业产品数量分布
    companies_with_product_counts = Company.objects.filter(is_active=True).annotate(
        product_count=Count('products')
    ).values('product_count').annotate(
        company_count=Count('id')
    ).order_by('product_count')
    
    # Top 10 企业（按产品数量）
    top_companies = Company.objects.filter(is_active=True).annotate(
        product_count=Count('products')
    ).filter(product_count__gt=0).order_by('-product_count')[:10]
    
    top_companies_data = []
    for company in top_companies:
        top_companies_data.append({
            'name': company.name,
            'name_en': company.name_en,
            'province': company.province,
            'product_count': company.product_count,
            'business_type': company.business_type
        })
    
    return Response({
        'company_statistics': {
            'total_companies': total_companies,
            'verified_companies': verified_companies,
            'verification_rate': round(verified_companies / total_companies * 100, 2) if total_companies > 0 else 0,
            'type_distribution': type_stats,
            'province_distribution': province_stats,
            'scale_distribution': scale_stats
        },
        'product_statistics': {
            'total_products': total_products,
            'active_products': active_products,
            'active_rate': round(active_products / total_products * 100, 2) if total_products > 0 else 0,
            'product_count_distribution': companies_with_product_counts
        },
        'top_companies': top_companies_data
    })


@extend_schema(
    summary="搜索企业",
    description="根据企业名称、国家等条件搜索企业"
)
@api_view(['GET'])
def search_companies(request):
    """搜索企业"""
    query = request.GET.get('q', '')
    province = request.GET.get('province', '')
    business_type = request.GET.get('business_type', '')
    
    queryset = Company.objects.filter(is_active=True)
    
    if query:
        queryset = queryset.filter(
            Q(name__icontains=query) |
            Q(name_en__icontains=query) |
            Q(description_cn__icontains=query) |
            Q(description_en__icontains=query)
        )
    
    if province:
        queryset = queryset.filter(province__icontains=province)
    
    if business_type:
        queryset = queryset.filter(business_type=business_type)
    
    # 按产品数量排序
    queryset = queryset.annotate(
        product_count=Count('products')
    ).order_by('-product_count', 'name')
    
    # 分页
    paginator = CompanyPagination()
    page = paginator.paginate_queryset(queryset, request)
    if page is not None:
        serializer = CompanySerializer(page, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    serializer = CompanySerializer(queryset, many=True)
    return Response(serializer.data)
