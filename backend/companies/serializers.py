from rest_framework import serializers
from drf_spectacular.utils import extend_schema_field
from typing import Optional, Dict, Any
from .models import Company, CompanyProduct


class CompanySerializer(serializers.ModelSerializer):
    """企业序列化器"""

    class Meta:
        model = Company
        fields = [
            'id', 'name', 'name_en', 'short_name', 'english_short_name', 'province', 'city',
            'website', 'business_type', 'company_scale', 'is_verified'
        ]


# ProductPriceSerializer已删除，价格信息直接在CompanyProduct中处理


class CompanyProductSerializer(serializers.ModelSerializer):
    """企业产品序列化器"""
    company = CompanySerializer(read_only=True)
    latest_price = serializers.SerializerMethodField()
    full_model_name = serializers.ReadOnlyField()
    chemical_cas = serializers.CharField(source='chemical.cas_number', read_only=True)
    chemical_name_cn = serializers.CharField(source='chemical.chinese_name', read_only=True)

    class Meta:
        model = CompanyProduct
        fields = [
            'id', 'company', 'chemical_cas', 'chemical_name_cn',
            'brand', 'model', 'full_model_name', 'assay',
            'physical_form', 'lifecycle_status',
            'applications', 'latest_price'
        ]
    
    @extend_schema_field(serializers.DictField)
    def get_latest_price(self, obj: CompanyProduct) -> Optional[Dict[str, Any]]:
        """获取最新价格 - 已删除价格历史功能"""
        # 价格信息现在直接存储在CompanyProduct中
        return {
            'price': obj.price_range,
            'currency': obj.currency,
            'unit': obj.unit
        } if obj.price_range else None


class CompanyProductListSerializer(serializers.ModelSerializer):
    """企业产品列表序列化器（简化版）"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    company_short_name = serializers.CharField(source='company.short_name', read_only=True)
    full_model_name = serializers.ReadOnlyField()
    chemical_cas = serializers.CharField(source='chemical.cas_number', read_only=True)

    class Meta:
        model = CompanyProduct
        fields = [
            'id', 'company_name', 'company_short_name', 'chemical_cas',
            'brand', 'model', 'full_model_name',
            'assay', 'physical_form', 'lifecycle_status'
        ]


class CompanyDetailSerializer(serializers.ModelSerializer):
    """企业详情序列化器"""
    products = CompanyProductListSerializer(many=True, read_only=True)
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = [
            'id', 'name', 'name_en', 'short_name', 'english_short_name', 'province', 'city',
            'website', 'product_page_url', 'contact_email', 'contact_phone',
            'description_cn', 'description_en', 'business_type', 'company_scale',
            'is_active', 'is_verified', 'created_at', 'last_scraped_at',
            'products', 'product_count'
        ]
        read_only_fields = ['created_at', 'last_scraped_at']
    
    @extend_schema_field(serializers.IntegerField)
    def get_product_count(self, obj: Company) -> int:
        """获取产品数量"""
        return obj.products.count()


class ChemicalProductSerializer(serializers.ModelSerializer):
    """化学品对应的企业产品序列化器（用于化学品详情页面）"""
    company_short_name = serializers.CharField(source='company.short_name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    full_model_name = serializers.ReadOnlyField()

    class Meta:
        model = CompanyProduct
        fields = [
            'id', 'company_short_name', 'company_name', 'full_model_name',
            'assay', 'physical_form', 'lifecycle_status', 'applications'
        ]
