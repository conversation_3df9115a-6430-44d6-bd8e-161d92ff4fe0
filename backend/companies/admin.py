from django.contrib import admin
from .models import Company, CompanyProduct


class CompanyProductInline(admin.TabularInline):
    model = CompanyProduct
    extra = 0
    fields = ['chemical', 'model', 'brand', 'lifecycle_status']


# ProductPriceInline已删除


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'name_en', 'business_type', 'is_active', 'created_at']
    list_filter = ['business_type', 'is_active', 'is_verified']
    search_fields = ['name', 'name_en', 'city']
    readonly_fields = ['created_at', 'updated_at', 'last_scraped_at']
    inlines = [CompanyProductInline]

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'name_en', 'province', 'city')
        }),
        ('联系信息', {
            'fields': ('website', 'product_page_url', 'contact_email', 'contact_phone')
        }),
        ('公司详情', {
            'fields': ('description_cn', 'description_en', 'business_type', 'company_scale')
        }),
        ('爬虫信息', {
            'fields': ('scraper_config', 'last_scraped_at'),
            'classes': ('collapse',)
        }),
        ('状态', {
            'fields': ('is_active', 'is_verified', 'created_at', 'updated_at')
        }),
    )


@admin.register(CompanyProduct)
class CompanyProductAdmin(admin.ModelAdmin):
    list_display = ['company', 'chemical', 'model', 'brand', 'lifecycle_status']
    list_filter = ['lifecycle_status']
    search_fields = ['company__name', 'chemical__cas_number', 'model', 'brand']
    readonly_fields = ['created_at', 'updated_at', 'last_verified_at']
    # inlines = [ProductPriceInline]  # 已删除

    fieldsets = (
        ('基本信息', {
            'fields': ('company', 'chemical', 'model', 'brand')
        }),
        ('产品规格', {
            'fields': ('assay', 'physical_form')
        }),
        ('供应状态', {
            'fields': ('lifecycle_status',)
        }),
        ('详细信息', {
            'fields': ('applications',),
            'classes': ('collapse',)
        }),
        ('元数据', {
            'fields': ('created_at', 'updated_at', 'last_verified_at')
        }),
    )


# ProductPriceAdmin已删除
