from django.db import models


class Company(models.Model):
    """有机过氧化物供应商公司模型"""
    name = models.CharField(max_length=200, help_text="公司中文名称")
    name_en = models.CharField(max_length=200, blank=True, help_text="公司英文名称")
    short_name = models.CharField(max_length=50, blank=True, help_text="公司缩写")
    english_short_name = models.CharField(max_length=50, blank=True, help_text="英文缩写")

    # 地理信息
    province = models.CharField(max_length=100, blank=True, help_text="省份")
    city = models.CharField(max_length=100, blank=True, help_text="城市")

    # 联系信息
    website = models.URLField(blank=True, help_text="官网地址")
    product_page_url = models.URLField(blank=True, help_text="产品页面URL")
    contact_email = models.EmailField(blank=True, help_text="联系邮箱")
    contact_phone = models.CharField(max_length=50, blank=True, help_text="联系电话")

    # 公司信息
    description_cn = models.TextField(blank=True, help_text="公司描述（中文）")
    description_en = models.TextField(blank=True, help_text="公司描述（英文）")
    business_type = models.CharField(
        max_length=50,
        choices=[
            ('manufacturer', '制造商'),
            ('distributor', '分销商'),
            ('trader', '贸易商'),
            ('supplier', '供应商'),
            ('other', '其他')
        ],
        default='manufacturer'
    )

    # 企业规模
    company_scale = models.CharField(
        max_length=30,
        choices=[
            ('跨国/全球企业', '跨国/全球企业'),
            ('large', '大型企业'),
            ('medium', '中型企业'),
            ('small', '小型企业'),
            ('unknown', '未知')
        ],
        default='unknown'
    )

    # 爬虫相关
    scraper_config = models.JSONField(default=dict, blank=True, help_text="爬虫配置信息")
    last_scraped_at = models.DateTimeField(null=True, blank=True, help_text="最后爬取时间")

    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True, help_text="是否活跃")
    is_verified = models.BooleanField(default=False, help_text="是否已验证")

    class Meta:
        db_table = 'companies'
        ordering = ['name']
        indexes = [
            models.Index(fields=['business_type']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.name_en})" if self.name_en else self.name


class CompanyProduct(models.Model):
    """企业有机过氧化物产品模型"""
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='products')
    chemical = models.ForeignKey('chemicals.ChemicalCompound', on_delete=models.CASCADE, related_name='company_products')

    # 产品基本信息 (对应CSV字段)
    model = models.CharField(max_length=100, blank=True, help_text="型号")
    brand = models.CharField(max_length=100, blank=True, help_text="品牌名称")
    assay = models.CharField(max_length=50, blank=True, help_text="含量")
    physical_form = models.CharField(max_length=100, blank=True, help_text="物理形态")
    applications = models.TextField(blank=True, help_text="应用领域")

    # 生命周期状态 (对应CSV字段)
    lifecycle_status = models.CharField(
        max_length=20,
        choices=[
            ('active', '在产'),
            ('discontinued', '停产'),
            ('stopped_selling', '停售'),
            ('limited_production', '限产'),
            ('trial_production', '试产'),
            ('in_development', '研发中'),
            ('planned_launch', '计划上市'),
            ('being_replaced', '替代中'),
            ('phased_out', '淘汰'),
            ('imported', '进口'),
            ('unknown', '未知')
        ],
        default='unknown',
        help_text="生命周期状态"
    )

    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_verified_at = models.DateTimeField(null=True, blank=True, help_text="最后验证时间")

    class Meta:
        db_table = 'company_products'
        unique_together = ['company', 'chemical', 'model']
        indexes = [
            models.Index(fields=['lifecycle_status']),
        ]

    def __str__(self):
        return f"{self.company.name} - {self.model or self.chemical.cas_number}"

    @property
    def full_model_name(self):
        """生成完整的产品型号：Brand + Model"""
        if self.brand and self.model:
            return f"{self.brand} {self.model}"
        elif self.model:
            return self.model
        else:
            return f"Product-{self.id}"


# ProductPrice模型已删除，价格信息直接存储在CompanyProduct中
