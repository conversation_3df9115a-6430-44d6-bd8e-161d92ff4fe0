from django.urls import path
from . import views

app_name = 'companies'

urlpatterns = [
    # 企业相关
    path('', views.CompanyListView.as_view(), name='company-list'),

    # 搜索和查询
    path('search/', views.search_companies, name='company-search'),
    path('suppliers/<str:cas_number>/', views.get_suppliers_by_cas, name='suppliers-by-cas'),

    # 统计信息
    path('statistics/', views.get_company_statistics, name='company-statistics'),

    # 企业详情（放在最后，避免匹配其他路径）
    path('<int:pk>/', views.CompanyDetailView.as_view(), name='company-detail-by-id'),
    path('<str:english_short_name>/', views.CompanyDetailView.as_view(), name='company-detail'),
    path('<int:pk>/products/', views.CompanyProductListView.as_view(), name='company-products-by-id'),
    path('<str:english_short_name>/products/', views.CompanyProductListView.as_view(), name='company-products'),
]
