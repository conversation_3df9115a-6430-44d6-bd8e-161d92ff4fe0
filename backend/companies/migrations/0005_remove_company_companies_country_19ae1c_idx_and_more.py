# Generated by Django 4.2.7 on 2025-07-29 23:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0004_company_english_short_name'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='company',
            name='companies_country_19ae1c_idx',
        ),
        migrations.RemoveIndex(
            model_name='companyproduct',
            name='company_pro_stock_s_5347ce_idx',
        ),
        migrations.RemoveIndex(
            model_name='companyproduct',
            name='company_pro_purity_bce880_idx',
        ),
        migrations.RemoveField(
            model_name='company',
            name='country',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='package_size',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='purity',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='stock_status',
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='lifecycle_status',
            field=models.CharField(choices=[('active', '在产'), ('pre_launch', '预发布'), ('discontinued', '停产'), ('phased_out', '逐步淘汰'), ('unknown', '未知')], default='unknown', help_text='生命周期状态', max_length=20),
        ),
        migrations.AddIndex(
            model_name='companyproduct',
            index=models.Index(fields=['lifecycle_status'], name='company_pro_lifecyc_b2aca2_idx'),
        ),
    ]
