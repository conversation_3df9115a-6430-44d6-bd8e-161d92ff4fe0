# Generated by Django 4.2.7 on 2025-07-30 01:24

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('chemicals', '0006_chemicalcompound_applications_and_more'),
        ('companies', '0006_remove_product_name_fields'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='companyproduct',
            name='company_pro_is_avai_ca9794_idx',
        ),
        migrations.AlterUniqueTogether(
            name='companyproduct',
            unique_together={('company', 'chemical', 'model')},
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='appearance',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='currency',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='is_available',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='lead_time',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='min_order_quantity',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='package_type',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='price_range',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='product_code',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='product_description',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='scraped_data',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='source_url',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='status',
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='technical_specs',
        ),
    ]
