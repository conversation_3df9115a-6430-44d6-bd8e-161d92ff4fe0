# Generated by Django 4.2.7 on 2025-07-28 21:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('chemicals', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='公司名称', max_length=200)),
                ('name_en', models.CharField(blank=True, help_text='英文名称', max_length=200)),
                ('country', models.CharField(help_text='国家', max_length=100)),
                ('city', models.CharField(blank=True, help_text='城市', max_length=100)),
                ('website', models.URLField(blank=True, help_text='官网地址')),
                ('contact_email', models.EmailField(blank=True, help_text='联系邮箱', max_length=254)),
                ('contact_phone', models.CharField(blank=True, help_text='联系电话', max_length=50)),
                ('description', models.TextField(blank=True, help_text='公司描述')),
                ('business_type', models.CharField(choices=[('manufacturer', '制造商'), ('distributor', '分销商'), ('trader', '贸易商'), ('other', '其他')], default='manufacturer', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_verified', models.BooleanField(default=False, help_text='是否已验证')),
            ],
            options={
                'db_table': 'companies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CompanyProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(blank=True, help_text='产品名称', max_length=200)),
                ('purity', models.CharField(blank=True, help_text='纯度', max_length=50)),
                ('package_size', models.CharField(blank=True, help_text='包装规格', max_length=100)),
                ('price_range', models.CharField(blank=True, help_text='价格范围', max_length=100)),
                ('is_available', models.BooleanField(default=True, help_text='是否有货')),
                ('lead_time', models.CharField(blank=True, help_text='交货期', max_length=50)),
                ('min_order_quantity', models.CharField(blank=True, help_text='最小订购量', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('chemical', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='chemicals.chemicalcompound')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='companies.company')),
            ],
            options={
                'db_table': 'company_products',
                'unique_together': {('company', 'chemical')},
            },
        ),
    ]
