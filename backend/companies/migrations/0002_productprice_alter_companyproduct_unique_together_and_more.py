# Generated by Django 4.2.7 on 2025-07-28 22:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chemicals', '0002_chemicalalias_and_more'),
        ('companies', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=2, help_text='价格', max_digits=12)),
                ('currency', models.CharField(default='CNY', help_text='货币', max_length=10)),
                ('unit', models.CharField(help_text='单位（如kg, ton等）', max_length=20)),
                ('price_type', models.CharField(choices=[('list_price', '标价'), ('quote_price', '报价'), ('market_price', '市场价'), ('bulk_price', '批发价')], default='list_price', max_length=20)),
                ('effective_date', models.DateField(help_text='生效日期')),
                ('source', models.CharField(blank=True, help_text='价格来源', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'product_prices',
                'ordering': ['-effective_date'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='companyproduct',
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name='company',
            name='description',
        ),
        migrations.AddField(
            model_name='company',
            name='company_scale',
            field=models.CharField(choices=[('large', '大型企业'), ('medium', '中型企业'), ('small', '小型企业'), ('unknown', '未知')], default='unknown', max_length=20),
        ),
        migrations.AddField(
            model_name='company',
            name='description_cn',
            field=models.TextField(blank=True, help_text='公司描述（中文）'),
        ),
        migrations.AddField(
            model_name='company',
            name='description_en',
            field=models.TextField(blank=True, help_text='公司描述（英文）'),
        ),
        migrations.AddField(
            model_name='company',
            name='is_active',
            field=models.BooleanField(default=True, help_text='是否活跃'),
        ),
        migrations.AddField(
            model_name='company',
            name='last_scraped_at',
            field=models.DateTimeField(blank=True, help_text='最后爬取时间', null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='product_page_url',
            field=models.URLField(blank=True, help_text='产品页面URL'),
        ),
        migrations.AddField(
            model_name='company',
            name='province',
            field=models.CharField(blank=True, help_text='省份', max_length=100),
        ),
        migrations.AddField(
            model_name='company',
            name='scraper_config',
            field=models.JSONField(blank=True, default=dict, help_text='爬虫配置信息'),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='appearance',
            field=models.CharField(blank=True, help_text='外观', max_length=100),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='applications',
            field=models.TextField(blank=True, help_text='应用领域'),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='currency',
            field=models.CharField(default='CNY', help_text='货币单位', max_length=10),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='last_verified_at',
            field=models.DateTimeField(blank=True, help_text='最后验证时间', null=True),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='package_type',
            field=models.CharField(blank=True, help_text='包装类型', max_length=100),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='product_code',
            field=models.CharField(blank=True, help_text='产品型号/代码', max_length=100),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='product_description',
            field=models.TextField(blank=True, help_text='产品描述'),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='product_name_cn',
            field=models.CharField(blank=True, help_text='产品中文名称', max_length=300),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='product_name_en',
            field=models.CharField(blank=True, help_text='产品英文名称', max_length=300),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='scraped_data',
            field=models.JSONField(blank=True, default=dict, help_text='原始爬取数据'),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='source_url',
            field=models.URLField(blank=True, help_text='产品来源URL'),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='stock_status',
            field=models.CharField(choices=[('in_stock', '现货'), ('pre_order', '预订'), ('out_of_stock', '缺货'), ('discontinued', '停产'), ('unknown', '未知')], default='unknown', max_length=20),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='technical_specs',
            field=models.JSONField(blank=True, default=dict, help_text='技术规格'),
        ),
        migrations.AlterField(
            model_name='company',
            name='business_type',
            field=models.CharField(choices=[('manufacturer', '制造商'), ('distributor', '分销商'), ('trader', '贸易商'), ('supplier', '供应商'), ('other', '其他')], default='manufacturer', max_length=50),
        ),
        migrations.AlterField(
            model_name='company',
            name='name',
            field=models.CharField(help_text='公司中文名称', max_length=200),
        ),
        migrations.AlterField(
            model_name='company',
            name='name_en',
            field=models.CharField(blank=True, help_text='公司英文名称', max_length=200),
        ),
        migrations.AlterField(
            model_name='companyproduct',
            name='chemical',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_products', to='chemicals.chemicalcompound'),
        ),
        migrations.AlterUniqueTogether(
            name='companyproduct',
            unique_together={('company', 'chemical', 'product_code')},
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['country'], name='companies_country_19ae1c_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['business_type'], name='companies_busines_99ea52_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['is_active'], name='companies_is_acti_e9a12b_idx'),
        ),
        migrations.AddIndex(
            model_name='companyproduct',
            index=models.Index(fields=['is_available'], name='company_pro_is_avai_ca9794_idx'),
        ),
        migrations.AddIndex(
            model_name='companyproduct',
            index=models.Index(fields=['stock_status'], name='company_pro_stock_s_5347ce_idx'),
        ),
        migrations.AddIndex(
            model_name='companyproduct',
            index=models.Index(fields=['purity'], name='company_pro_purity_bce880_idx'),
        ),
        migrations.AddField(
            model_name='productprice',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_history', to='companies.companyproduct'),
        ),
        migrations.RemoveField(
            model_name='companyproduct',
            name='product_name',
        ),
    ]
