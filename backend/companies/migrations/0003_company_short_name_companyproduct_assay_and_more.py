# Generated by Django 4.2.7 on 2025-07-29 22:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0002_productprice_alter_companyproduct_unique_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='short_name',
            field=models.CharField(blank=True, help_text='公司缩写', max_length=50),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='assay',
            field=models.CharField(blank=True, help_text='含量', max_length=50),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='brand',
            field=models.Char<PERSON>ield(blank=True, help_text='品牌名称', max_length=100),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='model',
            field=models.CharField(blank=True, help_text='型号', max_length=100),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='physical_form',
            field=models.<PERSON>r<PERSON><PERSON>(blank=True, help_text='物理形态', max_length=100),
        ),
        migrations.AddField(
            model_name='companyproduct',
            name='status',
            field=models.CharField(blank=True, help_text='产品状态', max_length=50),
        ),
    ]
