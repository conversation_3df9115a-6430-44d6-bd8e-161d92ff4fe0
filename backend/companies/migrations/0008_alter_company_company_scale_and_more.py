# Generated by Django 4.2.7 on 2025-07-30 21:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0007_remove_excess_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='company_scale',
            field=models.CharField(choices=[('multinational', '跨国/全球企业'), ('large', '大型企业'), ('medium', '中型企业'), ('small', '小型企业'), ('unknown', '未知')], default='unknown', max_length=30),
        ),
        migrations.AlterField(
            model_name='companyproduct',
            name='lifecycle_status',
            field=models.CharField(choices=[('active', '在产'), ('discontinued', '停产'), ('stopped_selling', '停售'), ('limited_production', '限产'), ('trial_production', '试产'), ('in_development', '研发中'), ('planned_launch', '计划上市'), ('being_replaced', '替代中'), ('phased_out', '淘汰'), ('imported', '进口'), ('unknown', '未知')], default='unknown', help_text='生命周期状态', max_length=20),
        ),
    ]
