from rest_framework import generics, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models_cn import OrganicPeroxide, OrganicPeroxideAlias
from .serializers_cn import (
    OrganicPeroxideListSerializer,
    OrganicPeroxideDetailSerializer
)


class OrganicPeroxidePagination(PageNumberPagination):
    """有机过氧化物分页器"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class OrganicPeroxideListView(generics.ListAPIView):
    """有机过氧化物列表视图"""
    queryset = OrganicPeroxide.objects.all()
    serializer_class = OrganicPeroxideListSerializer
    pagination_class = OrganicPeroxidePagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category']
    search_fields = ['cas_number', 'chinese_name', 'english_name', 'molecular_formula']
    ordering_fields = ['cas_number', 'created_at']
    ordering = ['cas_number']
    
    @extend_schema(
        summary="获取有机过氧化物列表",
        description="获取有机过氧化物列表，支持搜索、过滤和排序",
        parameters=[
            OpenApiParameter(
                name='search',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='搜索关键词（CAS号、中英文名称、分子式）'
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class OrganicPeroxideDetailView(generics.RetrieveAPIView):
    """有机过氧化物详情视图"""
    queryset = OrganicPeroxide.objects.prefetch_related('aliases')
    serializer_class = OrganicPeroxideDetailSerializer
    lookup_field = 'cas_number'
    
    @extend_schema(
        summary="获取有机过氧化物详情",
        description="根据CAS号获取有机过氧化物的详细信息"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@extend_schema(
    summary="搜索有机过氧化物",
    description="根据关键词搜索有机过氧化物"
)
@api_view(['GET'])
def search_organic_peroxides(request):
    """搜索有机过氧化物"""
    query = request.GET.get('q', '')
    
    if not query:
        return Response({'error': '请提供搜索关键词'}, status=400)
    
    # 构建查询条件
    queryset = OrganicPeroxide.objects.filter(
        Q(cas_number__icontains=query) |
        Q(chinese_name__icontains=query) |
        Q(english_name__icontains=query) |
        Q(molecular_formula__icontains=query) |
        Q(category__icontains=query)
    ).distinct()
    
    # 也搜索别名
    alias_matches = OrganicPeroxideAlias.objects.filter(
        alias_name__icontains=query
    ).select_related('compound')
    
    for alias in alias_matches:
        queryset = queryset | OrganicPeroxide.objects.filter(cas_number=alias.compound.cas_number)
    
    queryset = queryset.distinct().order_by('cas_number')
    
    # 分页
    paginator = OrganicPeroxidePagination()
    page = paginator.paginate_queryset(queryset, request)
    if page is not None:
        serializer = OrganicPeroxideListSerializer(page, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    serializer = OrganicPeroxideListSerializer(queryset, many=True)
    return Response(serializer.data)


@extend_schema(
    summary="获取统计信息",
    description="获取有机过氧化物数据库的统计信息"
)
@api_view(['GET'])
def get_statistics(request):
    """获取统计信息"""
    from django.db.models import Count

    # 基础统计
    total_compounds = OrganicPeroxide.objects.count()
    compounds_with_chinese = OrganicPeroxide.objects.exclude(chinese_name='').count()
    compounds_with_english = OrganicPeroxide.objects.exclude(english_name='').count()
    compounds_with_both = OrganicPeroxide.objects.exclude(chinese_name='').exclude(english_name='').count()

    # 别名统计
    total_aliases = OrganicPeroxideAlias.objects.count()
    chinese_aliases = OrganicPeroxideAlias.objects.filter(language='cn').count()
    english_aliases = OrganicPeroxideAlias.objects.filter(language='en').count()

    # 按类别统计
    category_stats = OrganicPeroxide.objects.values('category').annotate(
        count=Count('cas_number')
    ).order_by('-count')

    # 数据完整性统计
    data_completeness = {
        'chinese_only': OrganicPeroxide.objects.exclude(chinese_name='').filter(english_name='').count(),
        'english_only': OrganicPeroxide.objects.filter(chinese_name='').exclude(english_name='').count(),
        'both_languages': compounds_with_both,
        'no_data': OrganicPeroxide.objects.filter(chinese_name='').filter(english_name='').count()
    }

    return Response({
        # 基础统计
        'total_compounds': total_compounds,
        'compounds_with_chinese': compounds_with_chinese,
        'compounds_with_english': compounds_with_english,
        'compounds_with_both_languages': compounds_with_both,
        'data_completeness_rate': round(compounds_with_both / total_compounds * 100, 2) if total_compounds > 0 else 0,

        # 别名统计
        'total_aliases': total_aliases,
        'chinese_aliases': chinese_aliases,
        'english_aliases': english_aliases,
        'avg_aliases_per_compound': round(total_aliases / total_compounds, 2) if total_compounds > 0 else 0,

        # 分类统计
        'category_statistics': category_stats,
        'data_completeness': data_completeness
    })