from django.contrib import admin
from .models import ChemicalCompound, ChemicalAlias


class ChemicalAliasInline(admin.TabularInline):
    model = ChemicalAlias
    extra = 0


@admin.register(ChemicalCompound)
class ChemicalCompoundAdmin(admin.ModelAdmin):
    list_display = [
        'cas_number', 'chinese_name', 'english_name',
        'molecular_formula', 'category', 'created_at'
    ]
    list_filter = ['category', 'created_at']
    search_fields = ['cas_number', 'chinese_name', 'english_name', 'molecular_formula']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [ChemicalAliasInline]

    fieldsets = (
        ('基本信息', {
            'fields': ('cas_number', 'chinese_name', 'english_name', 'category')
        }),
        ('分子信息', {
            'fields': ('molecular_formula', 'molecular_weight', 'einecs_number', 'mdl_number')
        }),
        ('物理化学性质', {
            'fields': ('appearance', 'appearance_state', 'solubility', 'melting_point', 'boiling_point',
                      'density', 'vapor_density', 'vapor_pressure', 'refractive_index', 'flash_point',
                      'storage_conditions', 'stability', 'water_solubility', 'form', 'color'),
            'classes': ('collapse',)
        }),
        ('安全数据', {
            'fields': ('hazard_symbols_ghs', 'warning_words', 'hazard_description', 'prevention_statement',
                      'hazard_symbols', 'risk_codes', 'safety_statements', 'transport_number',
                      'wgk_germany', 'rtecs_number', 'hazard_class', 'packaging_group', 'auto_ignition_temp'),
            'classes': ('collapse',)
        }),
        ('应用和生产信息', {
            'fields': ('applications', 'production_method', 'exposure_limits'),
            'classes': ('collapse',)
        }),
        ('数据库引用', {
            'fields': ('merck', 'brn', 'cas_database', 'nist_info', 'epa_info', 'inchi_key', 'log_p',
                      'iarc_classification', 'tsca', 'customs_code', 'hazardous_substances_data'),
            'classes': ('collapse',)
        }),
        ('元数据', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ChemicalAlias)
class ChemicalAliasAdmin(admin.ModelAdmin):
    list_display = ['compound', 'alias_name', 'language', 'alias_type', 'source']
    list_filter = ['language', 'alias_type', 'source']
    search_fields = ['compound__cas_number', 'alias_name']


# ChemicalProperty模型已移除，属性直接存储在ChemicalCompound中