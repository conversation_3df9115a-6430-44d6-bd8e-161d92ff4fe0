from rest_framework import serializers
from drf_spectacular.utils import extend_schema_field
from typing import List, Dict, Any
from .models import ChemicalCompound, ChemicalAlias


class ChemicalAliasSerializer(serializers.ModelSerializer):
    """化学品别名序列化器"""

    class Meta:
        model = ChemicalAlias
        fields = [
            'alias_name', 'language', 'alias_type', 'source'
        ]


# ChemicalPropertySerializer已删除，属性直接在ChemicalCompound中处理


class ChemicalCompoundListSerializer(serializers.ModelSerializer):
    """化学品列表序列化器（简化版）"""
    data_completeness = serializers.SerializerMethodField()

    class Meta:
        model = ChemicalCompound
        fields = [
            'cas_number', 'chinese_name', 'english_name',
            'molecular_formula', 'molecular_weight', 'category', 'data_completeness'
        ]

    @extend_schema_field(serializers.IntegerField)
    def get_data_completeness(self, obj: ChemicalCompound) -> int:
        """获取数据完整性百分比"""
        return obj.calculate_data_completeness()


class ChemicalCompoundDetailSerializer(serializers.ModelSerializer):
    """化学品详情序列化器（完整版）"""
    data_completeness = serializers.SerializerMethodField()
    chinese_aliases = serializers.SerializerMethodField()
    english_aliases = serializers.SerializerMethodField()
    company_products = serializers.SerializerMethodField()

    class Meta:
        model = ChemicalCompound
        fields = [
            'cas_number', 'chinese_name', 'english_name', 'category',
            'molecular_formula', 'molecular_weight', 'einecs_number', 'mdl_number',
            'mol_file', 'update_date',
            'appearance', 'appearance_state', 'solubility', 'melting_point', 'boiling_point',
            'density', 'vapor_density', 'vapor_pressure', 'refractive_index', 'storage_conditions',
            'solubility_detail', 'form', 'color', 'odor', 'water_solubility',
            'brn', 'inchi_key', 'log_p', 'cas_database', 'iarc_classification', 'epa_info',
            'hazard_symbols_ghs', 'warning_words', 'hazard_description', 'prevention_statement',
            'hazard_symbols', 'risk_codes', 'safety_statements', 'transport_number',
            'wgk_germany', 'rtecs_number', 'auto_ignition_temp', 'tsca',
            'hazard_class', 'packaging_group', 'customs_code', 'hazardous_substances_data',
            'applications', 'stability',
            'created_at', 'updated_at',
            'data_completeness', 'chinese_aliases', 'english_aliases', 'company_products'
        ]
        read_only_fields = ['created_at', 'updated_at']

    @extend_schema_field(serializers.IntegerField)
    def get_data_completeness(self, obj: ChemicalCompound) -> int:
        """获取数据完整性百分比"""
        return obj.calculate_data_completeness()

    @extend_schema_field(serializers.ListField(child=serializers.CharField()))
    def get_chinese_aliases(self, obj: ChemicalCompound) -> List[str]:
        """获取中文别名"""
        return [alias.alias_name for alias in obj.aliases.filter(language='cn')]

    @extend_schema_field(serializers.ListField(child=serializers.CharField()))
    def get_english_aliases(self, obj: ChemicalCompound) -> List[str]:
        """获取英文别名"""
        return [alias.alias_name for alias in obj.aliases.filter(language='en')]

    @extend_schema_field(serializers.ListField)
    def get_company_products(self, obj: ChemicalCompound) -> List[Dict[str, Any]]:
        """获取企业产品信息"""
        from companies.serializers import ChemicalProductSerializer
        products = obj.company_products.all()
        return ChemicalProductSerializer(products, many=True).data




class ChemicalCompoundCreateSerializer(serializers.ModelSerializer):
    """化学品创建序列化器"""
    
    class Meta:
        model = ChemicalCompound
        fields = [
            'cas_number', 'chinese_name', 'english_name',
            'molecular_formula', 'molecular_weight',
            'melting_point', 'boiling_point', 'density',
            'storage_conditions', 'transport_number'
        ]
    
    def validate_cas_number(self, value):
        """验证CAS号格式"""
        import re
        if not re.match(r'^\d{2,7}-\d{2}-\d$', value):
            raise serializers.ValidationError("CAS号格式不正确，应为 XXXXXX-XX-X 格式")
        return value


class ChemicalSearchSerializer(serializers.Serializer):
    """化学品搜索序列化器"""
    query = serializers.CharField(max_length=200, required=False, help_text="搜索关键词")
    search_type = serializers.ChoiceField(
        choices=[
            ('all', '全部'),
            ('cas', 'CAS号'),
            ('name_cn', '中文名'),
            ('name_en', '英文名'),
            ('formula', '分子式')
        ],
        default='all',
        help_text="搜索类型"
    )
