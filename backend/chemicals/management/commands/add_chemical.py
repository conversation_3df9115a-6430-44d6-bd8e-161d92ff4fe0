from django.core.management.base import BaseCommand
from django.db import transaction
from chemicals.models_cn import OrganicPeroxide, OrganicPeroxideAlias


class Command(BaseCommand):
    help = '添加特定的有机过氧化物数据'

    def handle(self, *args, **options):
        # 准备化学品数据
        compound_data = {
            'cas_number': '10508-09-5',
            'chinese_name': '二叔戊基过氧化物',
            'english_name': 'Bis(1,1-dimethylpropyl) peroxide',
            'molecular_formula': 'C10H22O2',
            'molecular_weight': '174.28',
            'category': '无机化工：氧化物和过氧化物',
            'melting_point': '-55°C',
            'boiling_point': '146 °C(lit.)',
            'density': '0.818 g/mL at 25 °C(lit.)',
            'vapor_pressure': '14.7hPa at 25℃',
            'refractive_index': 'n20/D 1.409(lit.)',
            'flash_point': '77 °F',
            'water_solubility': '13.83mg/L at 20℃',
            'inchi_key': 'JJRDRFZYKKFYMO-UHFFFAOYSA-N',
            'log_p': '4.7 at 25℃',
            'nist_info': '(CH3)2C(C2H5)OOC(CH3)2C2H5(10508-09-5)',
            'epa_info': 'Peroxide, bis(1,1-dimethylpropyl) (10508-09-5)',
            'hazard_symbols_ghs': 'GHS02,GHS08,GHS07,GHS09',
            'warning_words': '警告',
            'hazard_description': 'H242-H226-H315-H411-H341',
            'prevention_statement': 'P201-P202-P281-P308+P313-P405-P501-P210-P220-P234-P280-P370+P378-P403+P235-P411-P420-P501-P264-P280-P302+P352-P321-P332+P313-P362',
            'hazard_symbols': 'O,Xn',
            'risk_codes': '8-22-38-7',
            'safety_statements': '7-16-17-36/37/39-47-14',
            'transport_number': 'UN 3107 5.2',
            'wgk_germany': '2',
            'rtecs_number': 'SD8300000',
            'hazard_class': '5.2',
            'packaging_group': 'II',
            'customs_code': '29096000',
            'toxicity': 'mouse,LD50,oral,1450mg/kg (1450mg/kg),"Prehled Prumyslove Toxikologie; Organicke Latky," Marhold, J., Prague, Czechoslovakia, Avicenum, 1986Vol. -, Pg. 263, 1986.',
        }
        
        success_count = 0
        error_count = 0
        
        with transaction.atomic():
            try:
                # 创建或更新化学品
                compound, created = OrganicPeroxide.objects.update_or_create(
                    cas_number='10508-09-5',
                    defaults=compound_data
                )
                
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'成功创建化学品: {compound.cas_number} - {compound.chinese_name}')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'成功更新化学品: {compound.cas_number} - {compound.chinese_name}')
                    )
                
                success_count += 1
                
                # 添加中文别名
                chinese_aliases = [
                    '过氧化二',
                    '引发剂DTAP',
                    '二特戊基过氧化物',
                    '引发剂DTAP,二叔戊基过氧化物',
                    '二(1,1-二甲基丙基)过氧化物',
                    '双(1,1-二甲基丙基)过氧化物',
                    'LUPEROX® DTA, 双叔戊过氧化物'
                ]
                
                for alias in chinese_aliases:
                    alias_obj, created = OrganicPeroxideAlias.objects.get_or_create(
                        compound=compound,
                        alias_name=alias,
                        language='cn',
                        alias_type='synonym'
                    )
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(f'添加中文别名: {alias}')
                        )
                
                # 添加英文别名
                english_aliases = [
                    'Di-tert-amyl peroxide',
                    'Di-tert-pentyl peroxide',
                    'Luperox DTA',
                    'Trigonox 201',
                    'DIAMYLPEROXIDE',
                    'tert-Amyl peroxide',
                    'tert-Pentyl peroxide',
                    'di-tert-pentyl-peroxid',
                    'Bis(tert-amyl) peroxide',
                    'Peroxide, di-tert-pentyl-'
                ]
                
                for alias in english_aliases:
                    alias_obj, created = OrganicPeroxideAlias.objects.get_or_create(
                        compound=compound,
                        alias_name=alias,
                        language='en',
                        alias_type='synonym'
                    )
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(f'添加英文别名: {alias}')
                        )
                
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'处理化学品时出错: {str(e)}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'操作完成! 成功: {success_count}, 失败: {error_count}'
            )
        )