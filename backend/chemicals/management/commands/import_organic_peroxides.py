from django.core.management.base import BaseCommand
from django.db import transaction
from chemicals.models_cn import OrganicPeroxide, OrganicPeroxideAlias
import json
import os


class Command(BaseCommand):
    help = '导入有机过氧化物数据'

    def add_arguments(self, parser):
        parser.add_argument('json_file', type=str, help='JSON文件路径')

    def handle(self, *args, **options):
        json_file = options['json_file']
        
        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(f'文件不存在: {json_file}')
            )
            return

        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        success_count = 0
        error_count = 0

        with transaction.atomic():
            for item in data:
                if 'error' in item:
                    error_count += 1
                    continue

                try:
                    # 准备化学品数据
                    compound_data = {
                        'cas_number': item.get('cas_number', ''),
                        'chinese_name': item.get('chinese_name', ''),
                        'english_name': item.get('english_name', ''),
                        'einecs_number': item.get('einecs_number', ''),
                        'molecular_formula': item.get('molecular_formula', ''),
                        'mdl_number': item.get('mdl_number', ''),
                        'molecular_weight': item.get('molecular_weight', ''),
                        'category': item.get('category', ''),
                        'appearance': item.get('appearance', ''),
                        'appearance_state': item.get('appearance_state', ''),
                        'solubility': item.get('solubility', ''),
                        'melting_point': item.get('melting_point', ''),
                        'boiling_point': item.get('boiling_point', ''),
                        'density': item.get('density', ''),
                        'vapor_pressure': item.get('vapor_pressure', ''),
                        'refractive_index': item.get('refractive_index', ''),
                        'flash_point': item.get('flash_point', ''),
                        'storage_conditions': item.get('storage_conditions', ''),
                        'solubility_detail': item.get('solubility_detail', ''),
                        'form': item.get('form', ''),
                        'color': item.get('color', ''),
                        'odor': item.get('odor', ''),
                        'water_solubility': item.get('water_solubility', ''),
                        'stability': item.get('stability', ''),
                        'merck': item.get('merck', ''),
                        'brn': item.get('brn', ''),
                        'inchi_key': item.get('inchi_key', ''),
                        'log_p': item.get('log_p', ''),
                        'cas_database': item.get('cas_database', ''),
                        'nist_info': item.get('nist_info', ''),
                        'epa_info': item.get('epa_info', ''),
                        'hazard_symbols_ghs': item.get('hazard_symbols_ghs', ''),
                        'warning_words': item.get('warning_words', ''),
                        'hazard_description': item.get('hazard_description', ''),
                        'prevention_statement': item.get('prevention_statement', ''),
                        'hazard_symbols': item.get('hazard_symbols', ''),
                        'risk_codes': item.get('risk_codes', ''),
                        'safety_statements': item.get('safety_statements', ''),
                        'transport_number': item.get('transport_number', ''),
                        'wgk_germany': item.get('wgk_germany', ''),
                        'rtecs_number': item.get('rtecs_number', ''),
                        'f_rating': item.get('f_rating', ''),
                        'hazard_class': item.get('hazard_class', ''),
                        'packaging_group': item.get('packaging_group', ''),
                        'customs_code': item.get('customs_code', ''),
                        'hazardous_substances_data': item.get('hazardous_substances_data', ''),
                        'toxicity': item.get('toxicity', ''),
                    }

                    # 创建或更新化学品
                    compound, created = OrganicPeroxide.objects.update_or_create(
                        cas_number=item['cas_number'],
                        defaults=compound_data
                    )

                    # 处理中文别名
                    chinese_aliases = item.get('chinese_aliases', [])
                    if chinese_aliases:
                        for alias in chinese_aliases:
                            OrganicPeroxideAlias.objects.get_or_create(
                                compound=compound,
                                alias_name=alias,
                                language='cn',
                                alias_type='synonym'
                            )

                    # 处理英文别名
                    english_aliases = item.get('english_aliases', [])
                    if english_aliases:
                        for alias in english_aliases:
                            OrganicPeroxideAlias.objects.get_or_create(
                                compound=compound,
                                alias_name=alias,
                                language='en',
                                alias_type='synonym'
                            )

                    success_count += 1
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(f'创建化学品: {item["cas_number"]}')
                        )
                    else:
                        self.stdout.write(
                            self.style.SUCCESS(f'更新化学品: {item["cas_number"]}')
                        )

                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'处理化学品 {item.get("cas_number", "unknown")} 时出错: {str(e)}')
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'导入完成! 成功: {success_count}, 失败: {error_count}'
            )
        )