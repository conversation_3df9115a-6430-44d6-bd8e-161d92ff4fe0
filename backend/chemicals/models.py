from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class ChemicalCompound(models.Model):
    """化学品化合物模型 - 根据实际HTML结构重新设计"""
    cas_number = models.CharField(max_length=20, primary_key=True, help_text="CAS号")

    # 基本信息
    chinese_name = models.CharField(max_length=200, blank=True, help_text="中文名称")
    english_name = models.CharField(max_length=200, blank=True, help_text="英文名称")
    einecs_number = models.CharField(max_length=50, blank=True, help_text="EINECS编号")
    molecular_formula = models.CharField(max_length=100, blank=True, help_text="分子式")
    mdl_number = models.CharField(max_length=50, blank=True, help_text="MDL编号")
    molecular_weight = models.CharField(max_length=50, blank=True, help_text="分子量")
    mol_file = models.Char<PERSON>ield(max_length=100, blank=True, help_text="MOL文件")
    update_date = models.CharField(max_length=50, blank=True, help_text="更新日期")
    category = models.CharField(max_length=200, blank=True, help_text="所属类别")

    # 物理化学性质
    appearance = models.TextField(blank=True, help_text="外观性质")
    appearance_state = models.TextField(blank=True, help_text="外观性状")
    solubility = models.TextField(blank=True, help_text="溶解性")
    melting_point = models.CharField(max_length=100, blank=True, help_text="熔点")
    boiling_point = models.CharField(max_length=100, blank=True, help_text="沸点")
    density = models.CharField(max_length=100, blank=True, help_text="密度")
    vapor_density = models.CharField(max_length=100, blank=True, help_text="蒸气密度")
    vapor_pressure = models.CharField(max_length=100, blank=True, help_text="蒸气压")
    refractive_index = models.CharField(max_length=100, blank=True, help_text="折射率")
    storage_conditions = models.TextField(blank=True, help_text="储存条件")
    solubility_detail = models.TextField(blank=True, help_text="溶解度")
    form = models.CharField(max_length=100, blank=True, help_text="形态")
    color = models.CharField(max_length=100, blank=True, help_text="颜色")
    odor = models.CharField(max_length=200, blank=True, help_text="气味")
    water_solubility = models.TextField(blank=True, help_text="水溶解性")
    stability = models.TextField(blank=True, help_text="稳定性")

    # 应用和生产信息
    applications = models.TextField(blank=True, help_text="用途")
    production_method = models.TextField(blank=True, help_text="生产方法")
    exposure_limits = models.TextField(blank=True, help_text="暴露限值")

    # 数据库引用
    brn = models.TextField(blank=True, help_text="BRN")
    inchi_key = models.TextField(blank=True, help_text="InChI Key")
    log_p = models.CharField(max_length=50, blank=True, help_text="LogP")
    cas_database = models.TextField(blank=True, help_text="CAS数据库")
    iarc_classification = models.TextField(blank=True, help_text="IARC致癌物分类")
    epa_info = models.TextField(blank=True, help_text="EPA化学物质信息")

    # 安全数据
    hazard_symbols_ghs = models.TextField(blank=True, help_text="危险性符号(GHS)")
    warning_words = models.TextField(blank=True, help_text="警示词")
    hazard_description = models.TextField(blank=True, help_text="危险性描述")
    prevention_statement = models.TextField(blank=True, help_text="防范说明")
    hazard_symbols = models.TextField(blank=True, help_text="危险品标志")
    risk_codes = models.TextField(blank=True, help_text="危险类别码")
    safety_statements = models.TextField(blank=True, help_text="安全说明")
    transport_number = models.TextField(blank=True, help_text="危险品运输编号")
    wgk_germany = models.CharField(max_length=50, blank=True, help_text="WGK Germany")
    rtecs_number = models.CharField(max_length=50, blank=True, help_text="RTECS号")
    auto_ignition_temp = models.CharField(max_length=50, blank=True, help_text="自燃温度")
    tsca = models.TextField(blank=True, help_text="TSCA")
    hazard_class = models.CharField(max_length=50, blank=True, help_text="危险等级")
    packaging_group = models.CharField(max_length=50, blank=True, help_text="包装类别")
    customs_code = models.CharField(max_length=50, blank=True, help_text="海关编码")
    hazardous_substances_data = models.TextField(blank=True, help_text="毒害物质数据")

    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'chemical_compounds'
        ordering = ['cas_number']
        indexes = [
            models.Index(fields=['chinese_name']),
            models.Index(fields=['english_name']),
            models.Index(fields=['molecular_formula']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return f"{self.cas_number} - {self.chinese_name or self.english_name}"

    def calculate_data_completeness(self):
        """计算数据完整性百分比"""
        # 获取所有需要检查的字段（排除主键、时间戳等元数据字段）
        excluded_fields = {'cas_number', 'created_at', 'updated_at'}

        # 获取模型的所有字段
        all_fields = [field.name for field in self._meta.fields if field.name not in excluded_fields]

        # 计算有值的字段数量
        filled_fields = 0
        for field_name in all_fields:
            value = getattr(self, field_name)
            # 检查字段是否有有效值
            if value and str(value).strip():
                filled_fields += 1

        # 计算完整性百分比
        total_fields = len(all_fields)
        if total_fields == 0:
            return 0

        completeness = (filled_fields / total_fields) * 100
        return round(completeness, 1)


class ChemicalAlias(models.Model):
    """化学品别名表 - 用于SEO优化"""
    compound = models.ForeignKey(ChemicalCompound, on_delete=models.CASCADE, related_name='aliases')
    alias_name = models.CharField(max_length=300, help_text="别名")
    language = models.CharField(
        max_length=10,
        choices=[('cn', '中文'), ('en', '英文')],
        help_text="语言"
    )
    alias_type = models.CharField(
        max_length=20,
        choices=[
            ('synonym', '同义词'),
            ('trade_name', '商品名'),
            ('common_name', '俗名'),
            ('iupac_name', 'IUPAC名称'),
            ('other', '其他')
        ],
        default='synonym',
        help_text="别名类型"
    )
    source = models.CharField(max_length=50, default='chemicalbook', help_text="数据来源")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'chemical_aliases'
        unique_together = ['compound', 'alias_name', 'language']
        indexes = [
            models.Index(fields=['alias_name']),
            models.Index(fields=['language']),
            models.Index(fields=['alias_type']),
        ]

    def __str__(self):
        return f"{self.compound.cas_number} - {self.alias_name} ({self.language})"


# ChemicalProperty模型已删除，属性直接存储在ChemicalCompound中