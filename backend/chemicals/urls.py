from django.urls import path
from . import views

app_name = 'chemicals'

urlpatterns = [
    # 搜索和统计（需要在具体路径之前）
    path('search/', views.search_chemicals, name='compound-search'),
    path('statistics/', views.get_statistics, name='statistics'),

    # 化学品CRUD操作
    path('', views.ChemicalCompoundListView.as_view(), name='compound-list'),
    path('<str:cas_number>/', views.ChemicalCompoundDetailView.as_view(), name='compound-detail'),
]
