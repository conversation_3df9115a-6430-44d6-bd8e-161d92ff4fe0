from rest_framework import generics, status, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import ChemicalCompound
from .serializers import (
    ChemicalCompoundListSerializer,
    ChemicalCompoundDetailSerializer,
    ChemicalCompoundCreateSerializer,
    ChemicalSearchSerializer
)


class ChemicalCompoundPagination(PageNumberPagination):
    """化学品分页器"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class ChemicalCompoundListView(generics.ListCreateAPIView):
    """化学品列表视图"""
    queryset = ChemicalCompound.objects.all()
    pagination_class = ChemicalCompoundPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category']
    search_fields = ['cas_number', 'chinese_name', 'english_name', 'molecular_formula']
    ordering_fields = ['cas_number', 'created_at']
    ordering = ['cas_number']
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ChemicalCompoundCreateSerializer
        return ChemicalCompoundListSerializer
    
    @extend_schema(
        summary="获取化学品列表",
        description="获取有机过氧化物化学品列表，支持搜索、过滤和排序",
        parameters=[
            OpenApiParameter(
                name='search',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='搜索关键词（CAS号、中英文名称、分子式）'
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="创建化学品",
        description="添加新的有机过氧化物化学品信息"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class ChemicalCompoundDetailView(generics.RetrieveUpdateDestroyAPIView):
    """化学品详情视图"""
    queryset = ChemicalCompound.objects.prefetch_related('aliases', 'company_products__company')
    serializer_class = ChemicalCompoundDetailSerializer
    lookup_field = 'cas_number'
    
    @extend_schema(
        summary="获取化学品详情",
        description="根据CAS号获取化学品的详细信息"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="更新化学品信息",
        description="更新指定CAS号的化学品信息"
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)
    
    @extend_schema(
        summary="部分更新化学品信息",
        description="部分更新指定CAS号的化学品信息"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)
    
    @extend_schema(
        summary="删除化学品",
        description="删除指定CAS号的化学品信息"
    )
    def delete(self, request, *args, **kwargs):
        return super().delete(request, *args, **kwargs)


@extend_schema(
    summary="高级搜索化学品",
    description="根据不同条件搜索有机过氧化物化学品",
    request=ChemicalSearchSerializer,
    responses=ChemicalCompoundListSerializer(many=True)
)
@api_view(['POST'])
def search_chemicals(request):
    """高级搜索化学品"""
    serializer = ChemicalSearchSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    query = serializer.validated_data.get('query', '')
    search_type = serializer.validated_data.get('search_type', 'all')
    
    # 初始化查询集
    queryset = ChemicalCompound.objects.all()

    # 按关键词搜索
    if query:
        if search_type == 'cas':
            queryset = queryset.filter(cas_number__icontains=query)
        elif search_type == 'name_cn':
            queryset = queryset.filter(chinese_name__icontains=query)
        elif search_type == 'name_en':
            queryset = queryset.filter(english_name__icontains=query)
        elif search_type == 'formula':
            queryset = queryset.filter(molecular_formula__icontains=query)
        else:  # 'all'
            queryset = queryset.filter(
                Q(cas_number__icontains=query) |
                Q(chinese_name__icontains=query) |
                Q(english_name__icontains=query) |
                Q(molecular_formula__icontains=query)
            )
    else:
        # 如果没有查询词但指定了搜索类型，则返回所有记录
        pass
    
    # 按CAS号排序
    queryset = queryset.order_by('cas_number')
    
    # 分页
    paginator = ChemicalCompoundPagination()
    page = paginator.paginate_queryset(queryset, request)
    if page is not None:
        serializer = ChemicalCompoundListSerializer(page, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    serializer = ChemicalCompoundListSerializer(queryset, many=True)
    return Response(serializer.data)


@extend_schema(
    summary="获取统计信息",
    description="获取有机过氧化物数据库的统计信息"
)
@api_view(['GET'])
def get_statistics(request):
    """获取统计信息"""
    from django.db.models import Count, Q, Avg
    from companies.models import Company, CompanyProduct

    # 基础统计
    total_compounds = ChemicalCompound.objects.count()
    compounds_with_chinese = ChemicalCompound.objects.exclude(chinese_name='').count()
    compounds_with_english = ChemicalCompound.objects.exclude(english_name='').count()
    compounds_with_both = ChemicalCompound.objects.exclude(chinese_name='').exclude(english_name='').count()

    # 企业统计
    total_companies = Company.objects.filter(is_active=True).count()
    companies_with_products = Company.objects.filter(products__isnull=False).distinct().count()

    # 产品统计
    total_products = CompanyProduct.objects.count()
    active_products = CompanyProduct.objects.filter(lifecycle_status='active').count()

    # 产品覆盖率
    compounds_with_products = ChemicalCompound.objects.filter(company_products__isnull=False).distinct().count()
    product_coverage_rate = round(compounds_with_products / total_compounds * 100, 2) if total_compounds > 0 else 0

    # 别名统计
    from .models import ChemicalAlias
    total_aliases = ChemicalAlias.objects.count()
    chinese_aliases = ChemicalAlias.objects.filter(language='cn').count()
    english_aliases = ChemicalAlias.objects.filter(language='en').count()

    # 按企业类型统计
    company_type_stats = Company.objects.filter(is_active=True).values('business_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # 按省份统计企业
    province_stats = Company.objects.filter(is_active=True, province__isnull=False).exclude(province='').values('province').annotate(
        count=Count('id')
    ).order_by('-count')

    # 数据完整性统计 - 使用数据库聚合优化性能
    # 计算完整度分布的更高效方式，避免遍历所有记录
    # 我们将使用注释字段来近似计算完整度
    
    # 先获取所有字段的数量（排除元数据字段）
    excluded_fields = {'cas_number', 'created_at', 'updated_at'}
    all_fields = [field.name for field in ChemicalCompound._meta.fields if field.name not in excluded_fields]
    total_fields_count = len(all_fields)
    
    # 使用数据库聚合计算不同完整度级别的化合物数量
    # 这里我们使用一个简化的计算方法，基于关键字段的填充情况
    high_completeness = ChemicalCompound.objects.exclude(
        chinese_name=''
    ).exclude(
        english_name=''
    ).exclude(
        molecular_formula=''
    ).count()
    
    # 中等完整度：至少有中文名或英文名，并且有分子式
    medium_completeness = ChemicalCompound.objects.filter(
        Q(chinese_name__isnull=False) | Q(english_name__isnull=False)
    ).exclude(
        molecular_formula=''
    ).count() - high_completeness
    
    # 低完整度：其他情况
    low_completeness = total_compounds - high_completeness - medium_completeness

    data_completeness = {
        'high_completeness': high_completeness,  # >=80%
        'medium_completeness': medium_completeness,  # 50-79%
        'low_completeness': low_completeness,  # <50%
        # 简化平均完整度计算，基于关键字段
        'avg_completeness': round(
            (high_completeness * 90 + medium_completeness * 65 + low_completeness * 25) / total_compounds, 
            1
        ) if total_compounds > 0 else 0
    }

    return Response({
        # 基础统计
        'total_compounds': total_compounds,
        'compounds_with_chinese': compounds_with_chinese,
        'compounds_with_english': compounds_with_english,
        'compounds_with_both_languages': compounds_with_both,

        # 企业统计
        'total_companies': total_companies,
        'companies_with_products': companies_with_products,
        'company_coverage_rate': round(companies_with_products / total_companies * 100, 2) if total_companies > 0 else 0,

        # 产品统计
        'total_products': total_products,
        'active_products': active_products,
        'compounds_with_products': compounds_with_products,
        'product_coverage_rate': product_coverage_rate,

        # 别名统计
        'total_aliases': total_aliases,
        'chinese_aliases': chinese_aliases,
        'english_aliases': english_aliases,
        'avg_aliases_per_compound': round(total_aliases / total_compounds, 2) if total_compounds > 0 else 0,

        # 分类统计
        'company_type_statistics': company_type_stats,
        'province_statistics': province_stats,
        'data_completeness': data_completeness
    })