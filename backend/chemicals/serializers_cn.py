from rest_framework import serializers
from .models_cn import OrganicPeroxide, OrganicPeroxideAlias


class OrganicPeroxideAliasSerializer(serializers.ModelSerializer):
    """有机过氧化物别名序列化器"""
    class Meta:
        model = OrganicPeroxideAlias
        fields = ['alias_name', 'language', 'alias_type']


class OrganicPeroxideListSerializer(serializers.ModelSerializer):
    """有机过氧化物列表序列化器"""
    class Meta:
        model = OrganicPeroxide
        fields = [
            'cas_number',
            'chinese_name',
            'english_name',
            'molecular_formula',
            'molecular_weight',
            'category',
            'created_at',
            'updated_at',
        ]


class OrganicPeroxideDetailSerializer(serializers.ModelSerializer):
    """有机过氧化物详情序列化器"""
    aliases = OrganicPeroxideAliasSerializer(many=True, read_only=True)
    
    class Meta:
        model = OrganicPeroxide
        fields = [
            # 基本信息
            'cas_number',
            'chinese_name',
            'english_name',
            'einecs_number',
            'molecular_formula',
            'mdl_number',
            'molecular_weight',
            'category',
            
            # 物理化学性质
            'appearance',
            'appearance_state',
            'solubility',
            'melting_point',
            'boiling_point',
            'density',
            'vapor_pressure',
            'refractive_index',
            'flash_point',
            'storage_conditions',
            'solubility_detail',
            'form',
            'color',
            'odor',
            'water_solubility',
            'stability',
            
            # 数据库引用
            'merck',
            'brn',
            'inchi_key',
            'log_p',
            'cas_database',
            'nist_info',
            'epa_info',
            
            # 安全数据
            'hazard_symbols_ghs',
            'warning_words',
            'hazard_description',
            'prevention_statement',
            'hazard_symbols',
            'risk_codes',
            'safety_statements',
            'transport_number',
            'wgk_germany',
            'rtecs_number',
            'f_rating',
            'hazard_class',
            'packaging_group',
            'customs_code',
            'hazardous_substances_data',
            'toxicity',
            
            # 关联信息
            'aliases',
            
            # 元数据
            'created_at',
            'updated_at',
        ]