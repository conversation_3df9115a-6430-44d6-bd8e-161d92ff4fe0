from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator


class OrganicPeroxide(models.Model):
    """有机过氧化物化学品主表 - 中文版"""
    cas_number = models.CharField(max_length=20, primary_key=True, help_text="CAS号")

    # 基本信息
    chinese_name = models.CharField(max_length=200, blank=True, help_text="中文名称")
    english_name = models.CharField(max_length=200, blank=True, help_text="英文名称")
    einecs_number = models.CharField(max_length=50, blank=True, help_text="EINECS编号")
    molecular_formula = models.CharField(max_length=100, blank=True, help_text="分子式")
    mdl_number = models.CharField(max_length=50, blank=True, help_text="MDL编号")
    molecular_weight = models.CharField(max_length=50, blank=True, help_text="分子量")
    category = models.Char<PERSON>ield(max_length=200, blank=True, help_text="所属类别")

    # 物理化学性质
    appearance = models.TextField(blank=True, help_text="外观性质")
    appearance_state = models.TextField(blank=True, help_text="外观性状")
    solubility = models.TextField(blank=True, help_text="溶解性")
    melting_point = models.CharField(max_length=100, blank=True, help_text="熔点")
    boiling_point = models.CharField(max_length=100, blank=True, help_text="沸点")
    density = models.CharField(max_length=100, blank=True, help_text="密度")
    vapor_pressure = models.CharField(max_length=100, blank=True, help_text="蒸气压")
    refractive_index = models.CharField(max_length=100, blank=True, help_text="折射率")
    flash_point = models.CharField(max_length=100, blank=True, help_text="闪点")
    storage_conditions = models.TextField(blank=True, help_text="储存条件")
    solubility_detail = models.TextField(blank=True, help_text="溶解度详情")
    form = models.CharField(max_length=100, blank=True, help_text="形态")
    color = models.CharField(max_length=100, blank=True, help_text="颜色")
    odor = models.CharField(max_length=100, blank=True, help_text="气味")
    water_solubility = models.CharField(max_length=100, blank=True, help_text="水溶解性")
    stability = models.TextField(blank=True, help_text="稳定性")

    # 数据库引用
    merck = models.CharField(max_length=100, blank=True, help_text="Merck")
    brn = models.CharField(max_length=100, blank=True, help_text="BRN")
    inchi_key = models.CharField(max_length=100, blank=True, help_text="InChI Key")
    log_p = models.CharField(max_length=50, blank=True, help_text="LogP")
    cas_database = models.CharField(max_length=200, blank=True, help_text="CAS数据库")
    nist_info = models.CharField(max_length=200, blank=True, help_text="NIST信息")
    epa_info = models.CharField(max_length=200, blank=True, help_text="EPA信息")

    # 安全数据
    hazard_symbols_ghs = models.CharField(max_length=200, blank=True, help_text="GHS危险符号")
    warning_words = models.CharField(max_length=100, blank=True, help_text="警示词")
    hazard_description = models.CharField(max_length=200, blank=True, help_text="危险性描述")
    prevention_statement = models.CharField(max_length=200, blank=True, help_text="防范说明")
    hazard_symbols = models.CharField(max_length=100, blank=True, help_text="危险品标志")
    risk_codes = models.CharField(max_length=200, blank=True, help_text="危险类别码")
    safety_statements = models.CharField(max_length=200, blank=True, help_text="安全说明")
    transport_number = models.CharField(max_length=100, blank=True, help_text="运输编号")
    wgk_germany = models.CharField(max_length=50, blank=True, help_text="WGK Germany")
    rtecs_number = models.CharField(max_length=50, blank=True, help_text="RTECS号")
    f_rating = models.CharField(max_length=50, blank=True, help_text="F等级")
    hazard_class = models.CharField(max_length=50, blank=True, help_text="危险等级")
    packaging_group = models.CharField(max_length=50, blank=True, help_text="包装类别")
    customs_code = models.CharField(max_length=50, blank=True, help_text="海关编码")
    hazardous_substances_data = models.CharField(max_length=200, blank=True, help_text="毒害物质数据")
    toxicity = models.TextField(blank=True, help_text="毒性")

    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'organic_peroxides'
        ordering = ['cas_number']
        indexes = [
            models.Index(fields=['chinese_name']),
            models.Index(fields=['english_name']),
            models.Index(fields=['molecular_formula']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return f"{self.cas_number} - {self.chinese_name or self.english_name}"


class OrganicPeroxideAlias(models.Model):
    """有机过氧化物别名表"""
    compound = models.ForeignKey(OrganicPeroxide, on_delete=models.CASCADE, related_name='aliases')
    alias_name = models.CharField(max_length=300, help_text="别名")
    language = models.CharField(
        max_length=10,
        choices=[('cn', '中文'), ('en', '英文')],
        help_text="语言"
    )
    alias_type = models.CharField(
        max_length=20,
        choices=[
            ('synonym', '同义词'),
            ('trade_name', '商品名'),
            ('common_name', '俗名'),
            ('iupac_name', 'IUPAC名称'),
            ('other', '其他')
        ],
        default='synonym',
        help_text="别名类型"
    )
    source = models.CharField(max_length=50, default='chemicalbook', help_text="数据来源")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'organic_peroxide_aliases'
        unique_together = ['compound', 'alias_name', 'language']
        indexes = [
            models.Index(fields=['alias_name']),
            models.Index(fields=['language']),
            models.Index(fields=['alias_type']),
        ]

    def __str__(self):
        return f"{self.compound.cas_number} - {self.alias_name} ({self.language})"