# Generated by Django 4.2.7 on 2025-07-28 22:02

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chemicals', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChemicalAlias',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alias_name', models.CharField(help_text='别名', max_length=300)),
                ('language', models.CharField(choices=[('cn', '中文'), ('en', '英文')], help_text='语言', max_length=10)),
                ('alias_type', models.CharField(choices=[('synonym', '同义词'), ('trade_name', '商品名'), ('common_name', '俗名'), ('iupac_name', 'IUPAC名称'), ('other', '其他')], default='synonym', help_text='别名类型', max_length=20)),
                ('source', models.CharField(default='chemicalbook', help_text='数据来源', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'chemical_aliases',
            },
        ),
        migrations.RemoveIndex(
            model_name='chemicalcompound',
            name='chemical_co_data_qu_238bcc_idx',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='applications',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='boiling_point',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='data_quality_score',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='data_source',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='density',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='flash_point',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='hazard_classification',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='industry_uses',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='melting_point',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='storage_conditions',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='transport_info',
        ),
        migrations.RemoveField(
            model_name='chemicalproperty',
            name='property_value',
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='applications_cn',
            field=models.TextField(blank=True, help_text='应用（中文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='applications_en',
            field=models.TextField(blank=True, help_text='应用（英文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='boiling_point_cn',
            field=models.CharField(blank=True, help_text='沸点（中文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='boiling_point_en',
            field=models.CharField(blank=True, help_text='沸点（英文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='density_cn',
            field=models.CharField(blank=True, help_text='密度（中文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='density_en',
            field=models.CharField(blank=True, help_text='密度（英文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='flash_point_cn',
            field=models.CharField(blank=True, help_text='闪点（中文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='flash_point_en',
            field=models.CharField(blank=True, help_text='闪点（英文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='has_chinese_data',
            field=models.BooleanField(default=False, help_text='是否有中文数据'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='has_english_data',
            field=models.BooleanField(default=False, help_text='是否有英文数据'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazard_classification_cn',
            field=models.TextField(blank=True, help_text='危险性分类（中文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazard_classification_en',
            field=models.TextField(blank=True, help_text='危险性分类（英文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='melting_point_cn',
            field=models.CharField(blank=True, help_text='熔点（中文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='melting_point_en',
            field=models.CharField(blank=True, help_text='熔点（英文）', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='storage_conditions_cn',
            field=models.TextField(blank=True, help_text='储存条件（中文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='storage_conditions_en',
            field=models.TextField(blank=True, help_text='储存条件（英文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='transport_info_cn',
            field=models.TextField(blank=True, help_text='运输信息（中文）'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='transport_info_en',
            field=models.TextField(blank=True, help_text='运输信息（英文）'),
        ),
        migrations.AddField(
            model_name='chemicalproperty',
            name='property_value_cn',
            field=models.TextField(blank=True, help_text='属性值（中文）'),
        ),
        migrations.AddField(
            model_name='chemicalproperty',
            name='property_value_en',
            field=models.TextField(blank=True, help_text='属性值（英文）'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='compound_name_cn',
            field=models.CharField(blank=True, help_text='中文标准名称', max_length=200),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='compound_name_en',
            field=models.CharField(blank=True, help_text='英文标准名称', max_length=200),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='molecular_formula',
            field=models.CharField(blank=True, help_text='分子式', max_length=100),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='molecular_weight',
            field=models.FloatField(blank=True, help_text='分子量', null=True, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='chemicalproperty',
            name='measurement_conditions',
            field=models.TextField(blank=True, help_text='测量条件'),
        ),
        migrations.AlterField(
            model_name='chemicalproperty',
            name='property_name',
            field=models.CharField(help_text='属性名称', max_length=100),
        ),
        migrations.AlterField(
            model_name='chemicalproperty',
            name='source',
            field=models.CharField(blank=True, help_text='数据来源', max_length=100),
        ),
        migrations.AlterField(
            model_name='chemicalproperty',
            name='unit',
            field=models.CharField(blank=True, help_text='单位', max_length=20),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['has_chinese_data'], name='chemical_co_has_chi_587668_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['has_english_data'], name='chemical_co_has_eng_709e7b_idx'),
        ),
        migrations.AddField(
            model_name='chemicalalias',
            name='compound',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='aliases', to='chemicals.chemicalcompound'),
        ),
        migrations.AddIndex(
            model_name='chemicalalias',
            index=models.Index(fields=['alias_name'], name='chemical_al_alias_n_8865ac_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalalias',
            index=models.Index(fields=['language'], name='chemical_al_languag_7d3aff_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalalias',
            index=models.Index(fields=['alias_type'], name='chemical_al_alias_t_c41cad_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='chemicalalias',
            unique_together={('compound', 'alias_name', 'language')},
        ),
    ]
