# Generated by Django 4.2.7 on 2025-07-29 08:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chemicals', '0004_organicperoxide_organicperoxidealias_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='f_rating',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='flash_point',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='merck',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='nist_info',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='stability',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='toxicity',
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='auto_ignition_temp',
            field=models.CharField(blank=True, help_text='自燃温度', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='iarc_classification',
            field=models.CharField(blank=True, help_text='IARC致癌物分类', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='mol_file',
            field=models.CharField(blank=True, help_text='MOL文件', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='tsca',
            field=models.CharField(blank=True, help_text='TSCA', max_length=10),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='update_date',
            field=models.CharField(blank=True, help_text='更新日期', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='vapor_density',
            field=models.CharField(blank=True, help_text='蒸气密度', max_length=100),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='cas_database',
            field=models.TextField(blank=True, help_text='CAS数据库'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='epa_info',
            field=models.TextField(blank=True, help_text='EPA化学物质信息'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='hazard_description',
            field=models.TextField(blank=True, help_text='危险性描述'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='hazard_symbols_ghs',
            field=models.CharField(blank=True, help_text='危险性符号(GHS)', max_length=200),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='hazardous_substances_data',
            field=models.TextField(blank=True, help_text='毒害物质数据'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='odor',
            field=models.CharField(blank=True, help_text='气味', max_length=200),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='prevention_statement',
            field=models.TextField(blank=True, help_text='防范说明'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='risk_codes',
            field=models.TextField(blank=True, help_text='危险类别码'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='safety_statements',
            field=models.TextField(blank=True, help_text='安全说明'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='solubility_detail',
            field=models.TextField(blank=True, help_text='溶解度'),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='transport_number',
            field=models.CharField(blank=True, help_text='危险品运输编号', max_length=100),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='water_solubility',
            field=models.TextField(blank=True, help_text='水溶解性'),
        ),
    ]
