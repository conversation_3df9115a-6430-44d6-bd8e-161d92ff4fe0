# Generated by Django 4.2.7 on 2025-07-28 21:12

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChemicalCompound',
            fields=[
                ('cas_number', models.CharField(help_text='CAS Registry Number', max_length=20, primary_key=True, serialize=False)),
                ('compound_name_cn', models.CharField(blank=True, help_text='Chinese name', max_length=200)),
                ('compound_name_en', models.CharField(blank=True, help_text='English name', max_length=200)),
                ('molecular_formula', models.CharField(blank=True, max_length=100)),
                ('molecular_weight', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('melting_point', models.CharField(blank=True, help_text='°C', max_length=50)),
                ('boiling_point', models.Char<PERSON>ield(blank=True, help_text='°C', max_length=50)),
                ('density', models.CharField(blank=True, help_text='g/cm³', max_length=50)),
                ('flash_point', models.CharField(blank=True, help_text='°C', max_length=50)),
                ('hazard_classification', models.TextField(blank=True)),
                ('storage_conditions', models.TextField(blank=True)),
                ('transport_info', models.TextField(blank=True)),
                ('applications', models.TextField(blank=True)),
                ('industry_uses', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('data_source', models.CharField(default='chemicalbook', max_length=100)),
                ('data_quality_score', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
            ],
            options={
                'db_table': 'chemical_compounds',
                'ordering': ['cas_number'],
            },
        ),
        migrations.CreateModel(
            name='ChemicalProperty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_name', models.CharField(max_length=100)),
                ('property_value', models.TextField()),
                ('unit', models.CharField(blank=True, max_length=20)),
                ('measurement_conditions', models.TextField(blank=True)),
                ('source', models.CharField(blank=True, max_length=100)),
                ('compound', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='properties', to='chemicals.chemicalcompound')),
            ],
            options={
                'db_table': 'chemical_properties',
            },
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['compound_name_en'], name='chemical_co_compoun_350444_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['compound_name_cn'], name='chemical_co_compoun_919185_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['molecular_formula'], name='chemical_co_molecul_e5e596_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['data_quality_score'], name='chemical_co_data_qu_238bcc_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='chemicalproperty',
            unique_together={('compound', 'property_name')},
        ),
    ]
