# Generated by Django 4.2.7 on 2025-07-29 02:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chemicals', '0002_chemicalalias_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='chemicalcompound',
            name='chemical_co_compoun_350444_idx',
        ),
        migrations.RemoveIndex(
            model_name='chemicalcompound',
            name='chemical_co_compoun_919185_idx',
        ),
        migrations.RemoveIndex(
            model_name='chemicalcompound',
            name='chemical_co_has_chi_587668_idx',
        ),
        migrations.RemoveIndex(
            model_name='chemicalcompound',
            name='chemical_co_has_eng_709e7b_idx',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='applications_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='applications_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='boiling_point_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='boiling_point_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='compound_name_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='compound_name_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='density_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='density_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='flash_point_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='flash_point_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='has_chinese_data',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='has_english_data',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='hazard_classification_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='hazard_classification_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='melting_point_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='melting_point_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='storage_conditions_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='storage_conditions_en',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='transport_info_cn',
        ),
        migrations.RemoveField(
            model_name='chemicalcompound',
            name='transport_info_en',
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='appearance',
            field=models.TextField(blank=True, help_text='外观性质'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='appearance_state',
            field=models.TextField(blank=True, help_text='外观性状'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='boiling_point',
            field=models.CharField(blank=True, help_text='沸点', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='brn',
            field=models.CharField(blank=True, help_text='BRN', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='cas_database',
            field=models.CharField(blank=True, help_text='CAS数据库', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='category',
            field=models.CharField(blank=True, help_text='所属类别', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='chinese_name',
            field=models.CharField(blank=True, help_text='中文名称', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='color',
            field=models.CharField(blank=True, help_text='颜色', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='customs_code',
            field=models.CharField(blank=True, help_text='海关编码', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='density',
            field=models.CharField(blank=True, help_text='密度', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='einecs_number',
            field=models.CharField(blank=True, help_text='EINECS编号', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='english_name',
            field=models.CharField(blank=True, help_text='英文名称', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='epa_info',
            field=models.CharField(blank=True, help_text='EPA信息', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='f_rating',
            field=models.CharField(blank=True, help_text='F等级', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='flash_point',
            field=models.CharField(blank=True, help_text='闪点', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='form',
            field=models.CharField(blank=True, help_text='形态', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazard_class',
            field=models.CharField(blank=True, help_text='危险等级', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazard_description',
            field=models.CharField(blank=True, help_text='危险性描述', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazard_symbols',
            field=models.CharField(blank=True, help_text='危险品标志', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazard_symbols_ghs',
            field=models.CharField(blank=True, help_text='GHS危险符号', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='hazardous_substances_data',
            field=models.CharField(blank=True, help_text='毒害物质数据', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='inchi_key',
            field=models.CharField(blank=True, help_text='InChI Key', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='log_p',
            field=models.CharField(blank=True, help_text='LogP', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='mdl_number',
            field=models.CharField(blank=True, help_text='MDL编号', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='melting_point',
            field=models.CharField(blank=True, help_text='熔点', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='merck',
            field=models.CharField(blank=True, help_text='Merck', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='nist_info',
            field=models.CharField(blank=True, help_text='NIST信息', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='odor',
            field=models.CharField(blank=True, help_text='气味', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='packaging_group',
            field=models.CharField(blank=True, help_text='包装类别', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='prevention_statement',
            field=models.CharField(blank=True, help_text='防范说明', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='refractive_index',
            field=models.CharField(blank=True, help_text='折射率', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='risk_codes',
            field=models.CharField(blank=True, help_text='危险类别码', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='rtecs_number',
            field=models.CharField(blank=True, help_text='RTECS号', max_length=50),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='safety_statements',
            field=models.CharField(blank=True, help_text='安全说明', max_length=200),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='solubility',
            field=models.TextField(blank=True, help_text='溶解性'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='solubility_detail',
            field=models.TextField(blank=True, help_text='溶解度详情'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='stability',
            field=models.TextField(blank=True, help_text='稳定性'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='storage_conditions',
            field=models.TextField(blank=True, help_text='储存条件'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='toxicity',
            field=models.TextField(blank=True, help_text='毒性'),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='transport_number',
            field=models.CharField(blank=True, help_text='运输编号', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='vapor_pressure',
            field=models.CharField(blank=True, help_text='蒸气压', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='warning_words',
            field=models.CharField(blank=True, help_text='警示词', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='water_solubility',
            field=models.CharField(blank=True, help_text='水溶解性', max_length=100),
        ),
        migrations.AddField(
            model_name='chemicalcompound',
            name='wgk_germany',
            field=models.CharField(blank=True, help_text='WGK Germany', max_length=50),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='cas_number',
            field=models.CharField(help_text='CAS号', max_length=20, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='chemicalcompound',
            name='molecular_weight',
            field=models.CharField(blank=True, default='', help_text='分子量', max_length=50),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['chinese_name'], name='chemical_co_chinese_272ddb_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['english_name'], name='chemical_co_english_297848_idx'),
        ),
        migrations.AddIndex(
            model_name='chemicalcompound',
            index=models.Index(fields=['category'], name='chemical_co_categor_e5b49a_idx'),
        ),
    ]
