# Generated by Django 4.2.23 on 2025-07-30 22:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('chemicals', '0007_alter_organicperoxidealias_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganicPeroxide',
            fields=[
                ('cas_number', models.CharField(help_text='CAS号', max_length=20, primary_key=True, serialize=False)),
                ('chinese_name', models.CharField(blank=True, help_text='中文名称', max_length=200)),
                ('english_name', models.Char<PERSON>ield(blank=True, help_text='英文名称', max_length=200)),
                ('einecs_number', models.CharField(blank=True, help_text='EINECS编号', max_length=50)),
                ('molecular_formula', models.CharField(blank=True, help_text='分子式', max_length=100)),
                ('mdl_number', models.Char<PERSON><PERSON>(blank=True, help_text='MDL编号', max_length=50)),
                ('molecular_weight', models.CharField(blank=True, help_text='分子量', max_length=50)),
                ('category', models.CharField(blank=True, help_text='所属类别', max_length=200)),
                ('appearance', models.TextField(blank=True, help_text='外观性质')),
                ('appearance_state', models.TextField(blank=True, help_text='外观性状')),
                ('solubility', models.TextField(blank=True, help_text='溶解性')),
                ('melting_point', models.CharField(blank=True, help_text='熔点', max_length=100)),
                ('boiling_point', models.CharField(blank=True, help_text='沸点', max_length=100)),
                ('density', models.CharField(blank=True, help_text='密度', max_length=100)),
                ('vapor_pressure', models.CharField(blank=True, help_text='蒸气压', max_length=100)),
                ('refractive_index', models.CharField(blank=True, help_text='折射率', max_length=100)),
                ('flash_point', models.CharField(blank=True, help_text='闪点', max_length=100)),
                ('storage_conditions', models.TextField(blank=True, help_text='储存条件')),
                ('solubility_detail', models.TextField(blank=True, help_text='溶解度详情')),
                ('form', models.CharField(blank=True, help_text='形态', max_length=100)),
                ('color', models.CharField(blank=True, help_text='颜色', max_length=100)),
                ('odor', models.CharField(blank=True, help_text='气味', max_length=100)),
                ('water_solubility', models.CharField(blank=True, help_text='水溶解性', max_length=100)),
                ('stability', models.TextField(blank=True, help_text='稳定性')),
                ('merck', models.CharField(blank=True, help_text='Merck', max_length=100)),
                ('brn', models.CharField(blank=True, help_text='BRN', max_length=100)),
                ('inchi_key', models.CharField(blank=True, help_text='InChI Key', max_length=100)),
                ('log_p', models.CharField(blank=True, help_text='LogP', max_length=50)),
                ('cas_database', models.CharField(blank=True, help_text='CAS数据库', max_length=200)),
                ('nist_info', models.CharField(blank=True, help_text='NIST信息', max_length=200)),
                ('epa_info', models.CharField(blank=True, help_text='EPA信息', max_length=200)),
                ('hazard_symbols_ghs', models.CharField(blank=True, help_text='GHS危险符号', max_length=200)),
                ('warning_words', models.CharField(blank=True, help_text='警示词', max_length=100)),
                ('hazard_description', models.CharField(blank=True, help_text='危险性描述', max_length=200)),
                ('prevention_statement', models.CharField(blank=True, help_text='防范说明', max_length=200)),
                ('hazard_symbols', models.CharField(blank=True, help_text='危险品标志', max_length=100)),
                ('risk_codes', models.CharField(blank=True, help_text='危险类别码', max_length=200)),
                ('safety_statements', models.CharField(blank=True, help_text='安全说明', max_length=200)),
                ('transport_number', models.CharField(blank=True, help_text='运输编号', max_length=100)),
                ('wgk_germany', models.CharField(blank=True, help_text='WGK Germany', max_length=50)),
                ('rtecs_number', models.CharField(blank=True, help_text='RTECS号', max_length=50)),
                ('f_rating', models.CharField(blank=True, help_text='F等级', max_length=50)),
                ('hazard_class', models.CharField(blank=True, help_text='危险等级', max_length=50)),
                ('packaging_group', models.CharField(blank=True, help_text='包装类别', max_length=50)),
                ('customs_code', models.CharField(blank=True, help_text='海关编码', max_length=50)),
                ('hazardous_substances_data', models.CharField(blank=True, help_text='毒害物质数据', max_length=200)),
                ('toxicity', models.TextField(blank=True, help_text='毒性')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'organic_peroxides',
                'ordering': ['cas_number'],
            },
        ),
        migrations.CreateModel(
            name='OrganicPeroxideAlias',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alias_name', models.CharField(help_text='别名', max_length=300)),
                ('language', models.CharField(choices=[('cn', '中文'), ('en', '英文')], help_text='语言', max_length=10)),
                ('alias_type', models.CharField(choices=[('synonym', '同义词'), ('trade_name', '商品名'), ('common_name', '俗名'), ('iupac_name', 'IUPAC名称'), ('other', '其他')], default='synonym', help_text='别名类型', max_length=20)),
                ('source', models.CharField(default='chemicalbook', help_text='数据来源', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('compound', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='aliases', to='chemicals.organicperoxide')),
            ],
            options={
                'db_table': 'organic_peroxide_aliases',
            },
        ),
        migrations.AddIndex(
            model_name='organicperoxide',
            index=models.Index(fields=['chinese_name'], name='organic_per_chines_4e2515_idx'),
        ),
        migrations.AddIndex(
            model_name='organicperoxide',
            index=models.Index(fields=['english_name'], name='organic_per_englis_840995_idx'),
        ),
        migrations.AddIndex(
            model_name='organicperoxide',
            index=models.Index(fields=['molecular_formula'], name='organic_per_molecul_0c977d_idx'),
        ),
        migrations.AddIndex(
            model_name='organicperoxide',
            index=models.Index(fields=['category'], name='organic_per_categor_75425d_idx'),
        ),
        migrations.AddIndex(
            model_name='organicperoxidealias',
            index=models.Index(fields=['alias_name'], name='organic_per_alias_n_601361_idx'),
        ),
        migrations.AddIndex(
            model_name='organicperoxidealias',
            index=models.Index(fields=['language'], name='organic_per_languag_5450a4_idx'),
        ),
        migrations.AddIndex(
            model_name='organicperoxidealias',
            index=models.Index(fields=['alias_type'], name='organic_per_alias_t_663563_idx'),
        ),
        migrations.AddConstraint(
            model_name='organicperoxidealias',
            constraint=models.UniqueConstraint(fields=('compound', 'alias_name', 'language'), name='unique_compound_alias_language'),
        ),
    ]