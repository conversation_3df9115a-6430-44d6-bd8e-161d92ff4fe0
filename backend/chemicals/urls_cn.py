from django.urls import path
from . import views_cn

app_name = 'organic_peroxides'

urlpatterns = [
    # 搜索和统计（需要在具体路径之前）
    path('search/', views_cn.search_organic_peroxides, name='peroxide-search'),
    path('statistics/', views_cn.get_statistics, name='statistics'),

    # 有机过氧化物CRUD操作
    path('', views_cn.OrganicPeroxideListView.as_view(), name='peroxide-list'),
    path('<str:cas_number>/', views_cn.OrganicPeroxideDetailView.as_view(), name='peroxide-detail'),
]